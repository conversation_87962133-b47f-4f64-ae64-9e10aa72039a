import {NET_GHG_EF_CONTENT} from 'containers/si/programs/content/kpi.net_ghg_emissions_factor.content';
import {
  makeNetGhgEFMetrics,
  NET_GHG_EF_METRICS_FORMATTER_MAP,
  NET_GHG_EF_METRICS_UNIT_MAP,
  type NetGhgEFMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_ghg_emissions_factor.transformation';
import {
  NO_DATA_FOR_MULTIPLE_CROPS_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

export const KPI = 'net_ghg_emissions_factor';
export const FORMATTER_MAP = NET_GHG_EF_METRICS_FORMATTER_MAP;
export const UNIT_MAP = NET_GHG_EF_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<
  MetricLookupMap<NetGhgEFMetrics>,
  'primaryMetricKey' | 'uncertaintyMetricKey'
> = {
  primaryMetricKey: 'netGhgEmissionsPerYield',
  uncertaintyMetricKey: 'netGhgEFStdErr',
};
export const TRANSFORMER = makeNetGhgEFMetrics;
export const CATEGORY_COLOR_KEY = NET_GHG_EF_CONTENT.categoryColor;

const CARD_CONTENT = {
  title: NET_GHG_EF_CONTENT.name,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
    isDownwardChangePositive: true,
    tooltipContent: NET_GHG_EF_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    isDisabledMessage: (_filters: KPIDataFiltersState) => undefined,
    fetchSummary: 'crop_type',
    tooltipContent: NET_GHG_EF_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'boundary',
    tooltipContent: NET_GHG_EF_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
