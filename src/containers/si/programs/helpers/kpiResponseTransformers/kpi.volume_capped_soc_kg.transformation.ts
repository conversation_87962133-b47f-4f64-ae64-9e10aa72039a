import {isNil} from '_common/utils/typeGuards';

import type {VolumeWeightedSocKgKPIMetricData} from 'containers/si/api/apiTypes';
import {CO2E} from 'containers/si/constants';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type VolumeCappedSocMetrics = {
  volumeCappedSocMass: Metric;
};

const private__volumeCappedSocUnit = () => getUnit('mt', CO2E);

export const VOLUME_CAPPED_SOC_METRICS_FORMATTER_MAP: Record<
  keyof VolumeCappedSocMetrics,
  (value: number) => string
> = {
  volumeCappedSocMass: formatAllNumbers,
};

export const VOLUME_CAPPED_SOC_METRICS_UNIT_MAP: Record<
  keyof VolumeCappedSocMetrics,
  () => UnitDetail<UnitType>
> = {
  volumeCappedSocMass: private__volumeCappedSocUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeVolumeWeightedSocMetrics);
 */
export const makeVolumeCappedSocMetrics = (
  data: VolumeWeightedSocKgKPIMetricData
): VolumeCappedSocMetrics | null => {
  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (isNil(data.volume_weighted_soc_kg) || !Number.isFinite(data.volume_weighted_soc_kg))
    return null;

  const value = convertValue({
    value: data.volume_weighted_soc_kg,
    from: 'kg',
    to: 'mt',
  });

  const unitDetails = VOLUME_CAPPED_SOC_METRICS_UNIT_MAP.volumeCappedSocMass();

  return {
    volumeCappedSocMass: {
      value,
      unit: unitDetails.unit,
      formattedValue: VOLUME_CAPPED_SOC_METRICS_FORMATTER_MAP.volumeCappedSocMass(value),
    },
  };
};
