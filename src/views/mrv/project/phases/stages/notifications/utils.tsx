import type {FieldNotifications, NotificationLevel, NotificationLookup} from './types';

export const clearFieldNotificationState = (
  previousState: FieldNotifications,
  fieldId: string,
  level?: NotificationLevel,
  notificationId?: string
) => {
  const previousFieldState = previousState[fieldId];

  if (!previousFieldState) {
    return previousState;
  }

  if (level) {
    const levelState = previousFieldState[level];

    if (!levelState) {
      return previousState;
    }

    if (!notificationId) {
      const updatedFieldState = {
        ...previousFieldState,
      };
      delete updatedFieldState[level];

      if (Object.keys(updatedFieldState).length === 0) {
        const newState = {...previousState};
        delete newState[fieldId];
        return newState;
      }

      return {
        ...previousState,
        [fieldId]: updatedFieldState,
      };
    }

    const newState = levelState.filter(n => n.id !== notificationId);
    if (newState.length === levelState.length) {
      return previousState;
    }

    if (newState.length === 0) {
      const updatedFieldState = {
        ...previousFieldState,
      };
      delete updatedFieldState[level];

      if (Object.keys(updatedFieldState).length === 0) {
        const newStateWithoutField = {...previousState};
        delete newStateWithoutField[fieldId];
        return newStateWithoutField;
      }

      return {
        ...previousState,
        [fieldId]: updatedFieldState,
      };
    }

    return {
      ...previousState,
      [fieldId]: {
        ...previousFieldState,
        [level]: newState,
      },
    };
  } else {
    const newState = {...previousState};
    delete newState[fieldId];
    return newState;
  }
};

export const clearPhaseNotificationState = (
  previousState: NotificationLookup,
  level?: NotificationLevel,
  notificationId?: string
) => {
  if (level) {
    const levelState = previousState[level];

    if (!levelState) {
      return previousState;
    }

    if (!notificationId) {
      const updatedState = {
        ...previousState,
      };
      delete updatedState[level];
      return updatedState;
    }

    const newState = levelState.filter(n => n.id !== notificationId);

    if (newState.length === 0) {
      const updatedState = {
        ...previousState,
      };
      delete updatedState[level];
      return updatedState;
    }

    return {
      ...previousState,
      [level]: newState,
    };
  } else {
    return {};
  }
};
