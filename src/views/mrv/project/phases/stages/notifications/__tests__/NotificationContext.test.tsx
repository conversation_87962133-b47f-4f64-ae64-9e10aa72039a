import {act, waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import type {ReactNode} from 'react';
import React from 'react';

import {SimpleProviders} from '_common/test_utils/renderWithProviders';

import {
  NotificationContextProvider,
  useNotificationContext,
} from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');

const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
  typeof useSelectedFieldContext
>;

const renderTestComponent = (children: ReactNode) => {
  return (
    <SimpleProviders>
      <NotificationContextProvider>{children}</NotificationContextProvider>
    </SimpleProviders>
  );
};

const renderHookWithProvider = () => {
  return renderHook(() => useNotificationContext(), {
    wrapper: ({children}) => renderTestComponent(children),
  });
};
const initialState = {
  selectedField: null,
  selectedFarm: null,
  farms: [],
  setSelectedField: jest.fn(),
  selectedFieldEvents: {field: null, events: null},
  setSelectedFieldEvents: jest.fn(),
  clearSelectedFieldEvents: jest.fn(),
  isLoading: false,
  error: null,
};

describe('NotificationContext', () => {
  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue(initialState);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should provide initial empty state', () => {
    const {result} = renderHookWithProvider();

    expect(result.current.fieldNotifications).toEqual({});
    expect(result.current.setNotificationForField).toBeInstanceOf(Function);
    expect(result.current.setNotificationForFields).toBeInstanceOf(Function);
    expect(result.current.clearNotificationForField).toBeInstanceOf(Function);
    expect(result.current.phaseNotification).toEqual({});
    expect(result.current.setPhaseNotification).toBeInstanceOf(Function);
    expect(result.current.clearPhaseNotification).toBeInstanceOf(Function);
  });

  it('should set notification for a single field', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'Success',
        message: 'Test success message',
      });
    });

    const notifications = result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
    expect(notifications).toHaveLength(1);
    expect(notifications?.[0]).toMatchObject({
      title: 'Success',
      message: 'Test success message',
    });
    expect(notifications?.[0]?.id).toBeDefined();
  });

  it('should set notifications for multiple fields', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForFields(['field-1', 'field-2'], NotificationLevel.SUCCESS, {
        title: 'Bulk Success',
        message: 'Bulk success message',
      });
    });

    const field1Notifications =
      result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
    const field2Notifications =
      result.current.fieldNotifications['field-2']?.[NotificationLevel.SUCCESS];

    expect(field1Notifications).toHaveLength(1);
    expect(field2Notifications).toHaveLength(1);
    expect(field1Notifications?.[0]).toMatchObject({
      title: 'Bulk Success',
      message: 'Bulk success message',
    });
    expect(field2Notifications?.[0]).toMatchObject({
      title: 'Bulk Success',
      message: 'Bulk success message',
    });
  });

  it('should append multiple notifications for the same field and level', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'First',
        message: 'First message',
      });
    });

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'Second',
        message: 'Second message',
      });
    });

    const notifications = result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
    expect(notifications).toHaveLength(2);
    expect(notifications?.[0]).toMatchObject({
      title: 'First',
      message: 'First message',
    });
    expect(notifications?.[1]).toMatchObject({
      title: 'Second',
      message: 'Second message',
    });
  });

  it('should handle different notification levels for the same field', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'Success',
        message: 'Success message',
      });
    });

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
        title: 'Error',
        message: 'Error message',
      });
    });

    const successNotifications =
      result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
    const errorNotifications =
      result.current.fieldNotifications['field-1']?.[NotificationLevel.ERROR];

    expect(successNotifications).toHaveLength(1);
    expect(errorNotifications).toHaveLength(1);
    expect(successNotifications?.[0]).toMatchObject({
      title: 'Success',
      message: 'Success message',
    });
    expect(errorNotifications?.[0]).toMatchObject({
      title: 'Error',
      message: 'Error message',
    });
  });

  it('should clear specific notification level for a field', () => {
    const {result} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'Success',
        message: 'Success message',
      });
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
        title: 'Error',
        message: 'Error message',
      });
    });

    // Clear only success notifications
    act(() => {
      result.current.clearNotificationForField('field-1', NotificationLevel.SUCCESS);
    });

    const errorNotifications =
      result.current.fieldNotifications['field-1']?.[NotificationLevel.ERROR];
    const successNotifications =
      result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];

    expect(successNotifications).toBeUndefined();
    expect(errorNotifications).toHaveLength(1);
    expect(errorNotifications?.[0]).toMatchObject({
      title: 'Error',
      message: 'Error message',
    });
  });

  it('should clear all notifications for a field when no level specified', () => {
    const {result} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'Success',
        message: 'Success message',
      });
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
        title: 'Error',
        message: 'Error message',
      });
      result.current.setNotificationForField('field-2', NotificationLevel.WARNING, {
        title: 'Warning',
        message: 'Warning message',
      });
    });

    // Clear all notifications for field-1
    act(() => {
      result.current.clearNotificationForField('field-1');
    });

    const field1Notifications = result.current.fieldNotifications['field-1'];
    const field2Notifications =
      result.current.fieldNotifications['field-2']?.[NotificationLevel.WARNING];

    expect(field1Notifications).toBeUndefined();
    expect(field2Notifications).toHaveLength(1);
    expect(field2Notifications?.[0]).toMatchObject({
      title: 'Warning',
      message: 'Warning message',
    });
  });

  it('should automatically clear success notifications when field is selected', async () => {
    const {result, rerender} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
        title: 'Success',
        message: 'Success message',
      });
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
        title: 'Error',
        message: 'Error message',
      });
    });

    // Simulate field selection
    mockUseSelectedFieldContext.mockReturnValue({
      selectedField: {id: 'field-1', name: 'Test Field'},
      selectedFarm: null,
      farms: [],
      setSelectedField: jest.fn(),
      selectedFieldEvents: {field: null, events: null},
      setSelectedFieldEvents: jest.fn(),
      clearSelectedFieldEvents: jest.fn(),
      isLoading: false,
      error: null,
    });

    rerender();

    await waitFor(() => {
      const successNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const errorNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.ERROR];

      expect(successNotifications).toBeUndefined();
      expect(errorNotifications).toHaveLength(1);
      expect(errorNotifications?.[0]).toMatchObject({
        title: 'Error',
        message: 'Error message',
      });
    });
  });

  it('should handle clearing notifications for non-existent field gracefully', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.clearNotificationForField('non-existent-field');
    });

    expect(result.current.fieldNotifications).toEqual({});
  });

  describe('setNotificationForFields', () => {
    it('should set notifications for multiple fields at once', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setNotificationForFields(
          ['field-1', 'field-2', 'field-3'],
          NotificationLevel.SUCCESS,
          {
            title: 'Bulk Success',
            message: 'Bulk success message',
          }
        );
      });

      const field1Notifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const field2Notifications =
        result.current.fieldNotifications['field-2']?.[NotificationLevel.SUCCESS];
      const field3Notifications =
        result.current.fieldNotifications['field-3']?.[NotificationLevel.SUCCESS];

      expect(field1Notifications).toHaveLength(1);
      expect(field2Notifications).toHaveLength(1);
      expect(field3Notifications).toHaveLength(1);

      [field1Notifications, field2Notifications, field3Notifications].forEach(notifications => {
        expect(notifications?.[0]).toMatchObject({
          title: 'Bulk Success',
          message: 'Bulk success message',
        });
      });
    });

    it('should append to existing notifications when setting for multiple fields', () => {
      const {result} = renderHookWithProvider();

      // Set initial notification for field-1
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Initial',
          message: 'Initial message',
        });
      });

      // Set bulk notifications including field-1
      act(() => {
        result.current.setNotificationForFields(['field-1', 'field-2'], NotificationLevel.SUCCESS, {
          title: 'Bulk',
          message: 'Bulk message',
        });
      });

      const field1Notifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const field2Notifications =
        result.current.fieldNotifications['field-2']?.[NotificationLevel.SUCCESS];

      expect(field1Notifications).toHaveLength(2);
      expect(field2Notifications).toHaveLength(1);

      expect(field1Notifications?.[0]).toMatchObject({
        title: 'Initial',
        message: 'Initial message',
      });
      expect(field1Notifications?.[1]).toMatchObject({
        title: 'Bulk',
        message: 'Bulk message',
      });
      expect(field2Notifications?.[0]).toMatchObject({
        title: 'Bulk',
        message: 'Bulk message',
      });
    });

    it('should handle empty field array gracefully', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setNotificationForFields([], NotificationLevel.SUCCESS, {
          title: 'No Fields',
          message: 'No fields message',
        });
      });

      expect(result.current.fieldNotifications).toEqual({});
    });

    it('should preserve existing notifications for different levels when setting bulk notifications', () => {
      const {result} = renderHookWithProvider();

      // Set initial error notification for field-1
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
          title: 'Error',
          message: 'Error message',
        });
      });

      // Set bulk success notifications
      act(() => {
        result.current.setNotificationForFields(['field-1', 'field-2'], NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
      });

      const field1ErrorNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.ERROR];
      const field1SuccessNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const field2SuccessNotifications =
        result.current.fieldNotifications['field-2']?.[NotificationLevel.SUCCESS];

      expect(field1ErrorNotifications).toHaveLength(1);
      expect(field1SuccessNotifications).toHaveLength(1);
      expect(field2SuccessNotifications).toHaveLength(1);

      expect(field1ErrorNotifications?.[0]).toMatchObject({
        title: 'Error',
        message: 'Error message',
      });
      expect(field1SuccessNotifications?.[0]).toMatchObject({
        title: 'Success',
        message: 'Success message',
      });
      expect(field2SuccessNotifications?.[0]).toMatchObject({
        title: 'Success',
        message: 'Success message',
      });
    });
  });

  describe('Auto-clear behavior', () => {
    it('should not clear success notifications if field has no success notifications', async () => {
      const {result, rerender} = renderHookWithProvider();

      // Set up error notification only
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
          title: 'Error',
          message: 'Error message',
        });
      });

      // Simulate field selection
      mockUseSelectedFieldContext.mockReturnValue({
        selectedField: {id: 'field-1', name: 'Test Field'},
        selectedFarm: null,
        farms: [],
        setSelectedField: jest.fn(),
        selectedFieldEvents: {field: null, events: null},
        setSelectedFieldEvents: jest.fn(),
        clearSelectedFieldEvents: jest.fn(),
        isLoading: false,
        error: null,
      });

      rerender();

      await waitFor(() => {
        const errorNotifications =
          result.current.fieldNotifications['field-1']?.[NotificationLevel.ERROR];
        expect(errorNotifications).toHaveLength(1);
        expect(errorNotifications?.[0]).toMatchObject({
          title: 'Error',
          message: 'Error message',
        });
      });
    });

    it('should not clear notifications when no field is selected', async () => {
      const {result, rerender} = renderHookWithProvider();

      // Set up notifications
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
      });

      // Simulate no field selection (selectedField remains null)
      rerender();

      await waitFor(() => {
        const successNotifications =
          result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
        expect(successNotifications).toHaveLength(1);
        expect(successNotifications?.[0]).toMatchObject({
          title: 'Success',
          message: 'Success message',
        });
      });
    });

    it('should only clear success notifications for the selected field, not other fields', async () => {
      const {result, rerender} = renderHookWithProvider();

      // Set up notifications for multiple fields
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Field 1',
          message: 'Field 1 success',
        });
        result.current.setNotificationForField('field-2', NotificationLevel.SUCCESS, {
          title: 'Field 2',
          message: 'Field 2 success',
        });
      });

      // Simulate field-1 selection
      mockUseSelectedFieldContext.mockReturnValue({
        selectedField: {id: 'field-1', name: 'Test Field 1'},
        selectedFarm: null,
        farms: [],
        setSelectedField: jest.fn(),
        selectedFieldEvents: {field: null, events: null},
        setSelectedFieldEvents: jest.fn(),
        clearSelectedFieldEvents: jest.fn(),
        isLoading: false,
        error: null,
      });

      rerender();

      await waitFor(() => {
        const field1SuccessNotifications =
          result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
        const field2SuccessNotifications =
          result.current.fieldNotifications['field-2']?.[NotificationLevel.SUCCESS];

        expect(field1SuccessNotifications).toBeUndefined();
        expect(field2SuccessNotifications).toHaveLength(1);
        expect(field2SuccessNotifications?.[0]).toMatchObject({
          title: 'Field 2',
          message: 'Field 2 success',
        });
      });
    });
  });

  describe('Phase Notifications', () => {
    it('should set phase notification', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Phase Success',
          message: 'Phase operation completed successfully',
        });
      });

      const notifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      expect(notifications).toHaveLength(1);
      expect(notifications?.[0]).toMatchObject({
        title: 'Phase Success',
        message: 'Phase operation completed successfully',
      });
      expect(notifications?.[0]?.id).toBeDefined();
    });

    it('should append multiple phase notifications for the same level', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'First Success',
          message: 'First phase success',
        });
      });

      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Second Success',
          message: 'Second phase success',
        });
      });

      const notifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      expect(notifications).toHaveLength(2);
      expect(notifications?.[0]).toMatchObject({
        title: 'First Success',
        message: 'First phase success',
      });
      expect(notifications?.[1]).toMatchObject({
        title: 'Second Success',
        message: 'Second phase success',
      });
    });

    it('should handle different notification levels for phase notifications', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Phase success message',
        });
      });

      act(() => {
        result.current.setPhaseNotification(NotificationLevel.ERROR, {
          title: 'Error',
          message: 'Phase error message',
        });
      });

      const successNotifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      const errorNotifications = result.current.phaseNotification[NotificationLevel.ERROR];

      expect(successNotifications).toHaveLength(1);
      expect(errorNotifications).toHaveLength(1);
      expect(successNotifications?.[0]).toMatchObject({
        title: 'Success',
        message: 'Phase success message',
      });
      expect(errorNotifications?.[0]).toMatchObject({
        title: 'Error',
        message: 'Phase error message',
      });
    });

    it('should clear all phase notifications for a specific level', () => {
      const {result} = renderHookWithProvider();

      // Set up notifications
      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success 1',
          message: 'First success',
        });
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success 2',
          message: 'Second success',
        });
        result.current.setPhaseNotification(NotificationLevel.ERROR, {
          title: 'Error',
          message: 'Error message',
        });
      });

      // Clear only success notifications
      act(() => {
        result.current.clearPhaseNotification(NotificationLevel.SUCCESS);
      });

      const successNotifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      const errorNotifications = result.current.phaseNotification[NotificationLevel.ERROR];

      expect(successNotifications).toBeUndefined();
      expect(errorNotifications).toHaveLength(1);
      expect(errorNotifications?.[0]).toMatchObject({
        title: 'Error',
        message: 'Error message',
      });
    });

    it('should clear all phase notifications when no level specified', () => {
      const {result} = renderHookWithProvider();

      // Set up notifications
      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
        result.current.setPhaseNotification(NotificationLevel.ERROR, {
          title: 'Error',
          message: 'Error message',
        });
      });

      // Clear all notifications
      act(() => {
        result.current.clearPhaseNotification();
      });

      expect(result.current.phaseNotification).toEqual({});
    });

    it('should clear specific phase notification by ID', () => {
      const {result} = renderHookWithProvider();

      // Set up notifications
      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success 1',
          message: 'First success',
        });
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success 2',
          message: 'Second success',
        });
      });

      const notifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      const firstNotificationId = notifications?.[0]?.id;

      // Clear specific notification by ID
      act(() => {
        result.current.clearPhaseNotification(NotificationLevel.SUCCESS, firstNotificationId);
      });

      const remainingNotifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      expect(remainingNotifications).toHaveLength(1);
      expect(remainingNotifications?.[0]).toMatchObject({
        title: 'Success 2',
        message: 'Second success',
      });
    });
  });

  describe('Clearing by ID', () => {
    it('should clear specific field notification by ID', () => {
      const {result} = renderHookWithProvider();

      // Set up notifications
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Success 1',
          message: 'First success',
        });
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Success 2',
          message: 'Second success',
        });
      });

      const notifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const firstNotificationId = notifications?.[0]?.id;

      // Clear specific notification by ID
      act(() => {
        result.current.clearNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          firstNotificationId
        );
      });

      const remainingNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      expect(remainingNotifications).toHaveLength(1);
      expect(remainingNotifications?.[0]).toMatchObject({
        title: 'Success 2',
        message: 'Second success',
      });
    });

    it('should remove field level when clearing last notification by ID', () => {
      const {result} = renderHookWithProvider();

      // Set up single notification
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
        result.current.setNotificationForField('field-1', NotificationLevel.ERROR, {
          title: 'Error',
          message: 'Error message',
        });
      });

      const notifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const notificationId = notifications?.[0]?.id;

      // Clear the only success notification by ID
      act(() => {
        result.current.clearNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          notificationId
        );
      });

      const successNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const errorNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.ERROR];

      expect(successNotifications).toBeUndefined();
      expect(errorNotifications).toHaveLength(1);
    });

    it('should remove field entirely when clearing last notification by ID', () => {
      const {result} = renderHookWithProvider();

      // Set up single notification
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
      });

      const notifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      const notificationId = notifications?.[0]?.id;

      // Clear the only notification by ID
      act(() => {
        result.current.clearNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          notificationId
        );
      });

      expect(result.current.fieldNotifications['field-1']).toBeUndefined();
    });

    it('should handle clearing non-existent notification ID gracefully', () => {
      const {result} = renderHookWithProvider();

      // Set up notification
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
      });

      const originalNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];

      // Try to clear with non-existent ID
      act(() => {
        result.current.clearNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          'non-existent-id'
        );
      });

      const remainingNotifications =
        result.current.fieldNotifications['field-1']?.[NotificationLevel.SUCCESS];
      expect(remainingNotifications).toEqual(originalNotifications);
    });

    it('should remove phase level when clearing last phase notification by ID', () => {
      const {result} = renderHookWithProvider();

      // Set up single notification
      act(() => {
        result.current.setPhaseNotification(NotificationLevel.SUCCESS, {
          title: 'Success',
          message: 'Success message',
        });
      });

      const notifications = result.current.phaseNotification[NotificationLevel.SUCCESS];
      const notificationId = notifications?.[0]?.id;

      // Clear the only notification by ID
      act(() => {
        result.current.clearPhaseNotification(NotificationLevel.SUCCESS, notificationId);
      });

      expect(result.current.phaseNotification[NotificationLevel.SUCCESS]).toBeUndefined();
    });
  });
});
