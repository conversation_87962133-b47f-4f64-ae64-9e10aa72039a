import {KPIVolumeCappedNetKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIVolumeCappedNetMetricsMock';
import {makeVolumeCappedNetMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_capped_net_kg.transformation';

describe('makeVolumeWeightedNetMetrics', () => {
  it('Should return VolumeWeightedNetMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makeVolumeCappedNetMetrics(KPIVolumeCappedNetKgSummarizeByCropTypeMock.crop_type_summary[1]!)
    ).toEqual({volumeCappedNetMass: {formattedValue: '0.01', unit: 'mt', value: 0.01}});
    expect(
      makeVolumeCappedNetMetrics(KPIVolumeCappedNetKgSummarizeByCropTypeMock.crop_type_summary[2]!)
    ).toEqual({volumeCappedNetMass: {formattedValue: '0.02', unit: 'mt', value: 0.02}});
    expect(
      makeVolumeCappedNetMetrics(KPIVolumeCappedNetKgSummarizeByCropTypeMock.crop_type_summary[3]!)
    ).toEqual({volumeCappedNetMass: {formattedValue: '0.03', unit: 'mt', value: 0.03}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeCappedNetMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_net_kg: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeCappedNetMetrics({
        volume_weighted_net_kg: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeCappedNetMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeCappedNetMetrics({
        volume_weighted_net_kg: Infinity,
      })
    ).toEqual(null);
  });
});
