import uniq from 'lodash/uniq';
import {useMemo} from 'react';

import {getTypedValues} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {
  CARD_CONTENT,
  CATEGORY_COLOR_KEY,
  FORMATTER_MAP,
  KPI,
  TRANSFORMER as kpiTransformer,
  METRIC_MAP,
  PANEL_CONTENT,
  UNIT_MAP,
} from 'containers/si/programs/report/kpi/components/VolumeWeightedNetEFCard/content';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useHasPurchaseVolume} from 'containers/si/programs/report/kpi/hooks/useHasPurchaseVolume';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import type {SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type UseVolumeWeightedNetEFCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    hasPurchaseVolume: boolean;
  };
};

export const useVolumeWeightedNetEFCardData = (): UseVolumeWeightedNetEFCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const {hasPurchaseVolume} = useHasPurchaseVolume();

  const summariesForFetch = useMemo(
    () =>
      uniq(
        getTypedValues(PANEL_CONTENT)
          .map(({isDisabledMessage, fetchSummary}) =>
            !isDisabledMessage(filtersState, hasPurchaseVolume) ? fetchSummary : undefined
          )
          .filter(isDefined)
      ),
    [filtersState, hasPurchaseVolume]
  );

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: summariesForFetch,
    commonFilters,
    kpiTransformer,
  });
  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
      isDownwardChangePositive: CARD_CONTENT.isDownwardChangePositive,
    });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
    hasPurchaseVolume,
  };

  return {isLoading, data};
};
