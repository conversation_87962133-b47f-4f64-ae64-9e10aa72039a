import React from 'react';



import { Alert, AlertTitle, SvgIcon } from '@regrow-internal/design-system';



import { useNotificationContext } from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import { NotificationLevel } from 'views/mrv/project/phases/stages/notifications/types';





export const PhaseAlert: React.FC = () => {
  const {phaseNotification, clearPhaseNotification} = useNotificationContext();

  const {error, warning, success} = phaseNotification;

  const shouldShowAlerts = Object.keys(phaseNotification).length > 0;

  const handleClear = (level: NotificationLevel, id: string) => {
    clearPhaseNotification(level, id);
  };

  if (shouldShowAlerts) {
    return (
      <>
        {error?.length
          ? error.map(({title, message, id}) => (
              <Alert
                key={id}
                severity={'error'}
                icon={<SvgIcon type="cross-circled" />}
                onClose={() => {
                  handleClear(NotificationLevel.ERROR, id);
                }}
              >
                <AlertTitle>{title}</AlertTitle>
                {message}
              </Alert>
            ))
          : null}

        {warning?.length
          ? warning.map(({title, message, id}) => (
              <Alert
                key={id}
                severity={'warning'}
                icon={<SvgIcon type="warning-triangled" />}
                onClose={() => {
                  handleClear(NotificationLevel.WARNING, id);
                }}
              >
                <AlertTitle>{title}</AlertTitle>
                {message}
              </Alert>
            ))
          : null}

        {success?.length
          ? success.map(({title, message, id}) => (
              <Alert
                key={id}
                severity={'success'}
                icon={<SvgIcon type="check-mark-circled" />}
                onClose={() => {
                  handleClear(NotificationLevel.SUCCESS, id);
                }}
              >
                <AlertTitle>{title}</AlertTitle>
                {message}
              </Alert>
            ))
          : null}
      </>
    );
  }

  return null;
};