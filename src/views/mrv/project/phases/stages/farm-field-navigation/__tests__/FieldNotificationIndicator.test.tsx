import {screen} from '@testing-library/react';
import React from 'react';

import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {FieldNotificationIndicator} from 'views/mrv/project/phases/stages/farm-field-navigation/FieldNotificationIndicator';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';

describe('FieldNotificationIndicator', () => {
  it('should render nothing when no notifications are provided', () => {
    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={null} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should render nothing when notifications object is empty', () => {
    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={{}} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should render success indicator when only success notifications exist', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: ['Success message'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('success')).toBeInTheDocument();
  });

  it('should render warning indicator when only warning notifications exist', () => {
    const notifications = {
      [NotificationLevel.WARNING]: ['Warning message'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should render error indicator when only error notifications exist', () => {
    const notifications = {
      [NotificationLevel.ERROR]: ['Error message'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('error')).toBeInTheDocument();
  });

  it('should prioritize error over warning and success', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: ['Success message'],
      [NotificationLevel.WARNING]: ['Warning message'],
      [NotificationLevel.ERROR]: ['Error message'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('error')).toBeInTheDocument();
  });

  it('should prioritize warning over success when no error exists', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: ['Success message'],
      [NotificationLevel.WARNING]: ['Warning message'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should render nothing when notification arrays are empty', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [],
      [NotificationLevel.WARNING]: [],
      [NotificationLevel.ERROR]: [],
    };

    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={notifications} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should handle multiple messages of the same level', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: ['First success', 'Second success'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('success')).toBeInTheDocument();
  });

  it('should handle undefined notification arrays gracefully', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: undefined,
      [NotificationLevel.WARNING]: ['Warning message'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should handle mixed empty and populated arrays', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [],
      [NotificationLevel.WARNING]: ['Warning message'],
      [NotificationLevel.ERROR]: [],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should handle multiple messages across different levels and prioritize correctly', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: ['Success 1', 'Success 2'],
      [NotificationLevel.WARNING]: ['Warning 1', 'Warning 2'],
      [NotificationLevel.ERROR]: ['Error 1', 'Error 2'],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    // Should still prioritize error even with multiple messages
    expect(screen.getByLabelText('error')).toBeInTheDocument();
  });

  it('should render nothing when all notification arrays are explicitly undefined', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: undefined,
      [NotificationLevel.WARNING]: undefined,
      [NotificationLevel.ERROR]: undefined,
    };

    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={notifications} />
    );
    expect(container.firstChild).toBeNull();
  });
});
