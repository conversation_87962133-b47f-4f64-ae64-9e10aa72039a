import type {VolumeWeightedGhgKgKPIResponse} from 'containers/si/api/apiTypes';

const KPIVolumeCappedGhgKgMock: VolumeWeightedGhgKgKPIResponse = {
  kpi_name: 'volume_weighted_ghg_kg',
  year: 2023,
  metric: {
    volume_weighted_ghg_kg: 100,
  },
  units: 'kg',
  boundary_type: 'subsection',
};

export const KPIVolumeCappedGhgKgSummarizeByCropTypeMock: VolumeWeightedGhgKgKPIResponse &
  Required<Pick<VolumeWeightedGhgKgKPIResponse, 'crop_type_summary'>> = {
  ...KPIVolumeCappedGhgKgMock,
  crop_type_summary: {
    1: {volume_weighted_ghg_kg: 10},
    2: {volume_weighted_ghg_kg: 20},
    3: {volume_weighted_ghg_kg: 30},
  },
};
