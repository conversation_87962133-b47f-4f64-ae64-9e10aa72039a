import { fireEvent, screen } from '@testing-library/react';
import React from 'react';



import { StageTypes } from '__generated__/mrv/mrvApi.types';
import { renderWithProviders } from '_common/test_utils/renderWithProviders';



import {useNavigateToStage} from 'containers/mrv/_hooks/useNavigateToStage';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {SelectedEventsSnackbar} from 'views/mrv/project/phases/stages/field-events/update-events/SelectedEventsSnackbar';
import type {SelectedFieldEventContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('containers/mrv/_hooks/useNavigateToStage');
jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock(
  'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents',
  () => ({
    useCopyFieldEvents: () => ({
      copyFieldEvents: jest.fn(),
      copyFieldEventsLoading: false,
    }),
  })
);

const field = {
  id: '123',
  name: 'Test Field',
  area: 100,
  core_farm_group_id: 1,
  geometry: null,
};
const createMockSelectedFieldEvents = (
  events: SelectedFieldEventContext['events'] = null,
  fieldName = 'Test Field'
): SelectedFieldEventContext => ({
  field: {
    ...field,
    name: fieldName,
  },
  events,
});

const defaultContext = {
  selectedFieldEvents: {
    field: null,
    events: null,
  },
  setSelectedField: jest.fn(),
  clearSelectedFieldEvents: jest.fn(),
};

const mockStages = [
  {id: 123, type: StageTypes.CROP_EVENTS},
  {id: 234, type: StageTypes.TILLAGE_EVENTS},
  {id: 345, type: StageTypes.NUTRIENT_EVENTS},
  {id: 456, type: StageTypes.IRRIGATION_EVENTS},
];

describe('SelectedEventsSnackbar', () => {
  const mockNavigateToStage = jest.fn();
  let test: any;

  beforeEach(() => {
    test = {};

    test.usePhaseContext = usePhaseContext as jest.MockedFunction<typeof usePhaseContext>;
    test.usePhaseContext.mockReturnValue({
      currentPhase: {
        id: '1',
        stages: mockStages,
      },
    });

    test.useNavigateToStage = useNavigateToStage as jest.MockedFunction<typeof useNavigateToStage>;

    test.useSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
      typeof useSelectedFieldContext
    >;
    test.useSelectedFieldContext.mockReturnValue(defaultContext);
    test.useNavigateToStage.mockReturnValue({
      navigateToStage: mockNavigateToStage,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should not render when selectedFieldEvents is null', () => {
    renderWithProviders(<SelectedEventsSnackbar />);

    expect(screen.queryByRole('presentation')).not.toBeInTheDocument();
  });

  it('should not render when no events are selected', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [FieldEventType.CroppingEvent]: [],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    expect(screen.queryByRole('presentation')).not.toBeInTheDocument();
  });

  it('should render selected events panel when events are selected', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1', '2'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    expect(screen.getByRole('presentation', {name: 'selected events panel'})).toBeInTheDocument();
  });

  it('should display correct total count and field name', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents(
      {
        [StageTypes.CROP_EVENTS]: ['1', '2'],
        [StageTypes.TILLAGE_EVENTS]: ['3'],
        [StageTypes.NUTRIENT_EVENTS]: [],
        [StageTypes.IRRIGATION_EVENTS]: ['4', '5', '6'],
      },
      'My Test Field'
    );

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    expect(screen.getByText(/6 events/)).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'My Test Field'})).toBeInTheDocument();
  });

  it('should display event counts for each stage type', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1', '2'],
      [StageTypes.TILLAGE_EVENTS]: ['3'],
      [StageTypes.NUTRIENT_EVENTS]: [],
      [StageTypes.IRRIGATION_EVENTS]: ['4', '5', '6'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    expect(screen.getByText('Crops (2)')).toBeInTheDocument();
    expect(screen.getByText('Tillage (1)')).toBeInTheDocument();
    expect(screen.getByText('Nutrients (0)')).toBeInTheDocument();
    expect(screen.getByText('Irrigation (3)')).toBeInTheDocument();
  });

  it('should only show stages that exist in current phase', () => {
    const limitedPhaseContext = {
      currentPhase: {
        id: '1',
        stages: mockStages.slice(0, 2),
      },
    };
    test.usePhaseContext.mockReturnValue(limitedPhaseContext);

    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1', '2'],
      [StageTypes.TILLAGE_EVENTS]: ['3'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    expect(screen.getByText(/3 events/)).toBeInTheDocument();
    expect(screen.getByRole('button', {name: field.name})).toBeInTheDocument();
    expect(screen.getByText('Crops (2)')).toBeInTheDocument();
    expect(screen.getByText('Tillage (1)')).toBeInTheDocument();
    expect(screen.queryByText(/Nutrients/)).not.toBeInTheDocument();
    expect(screen.queryByText(/Irrigation/)).not.toBeInTheDocument();
  });

  it('should call onClose when close button is clicked', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    const closeButton = screen.getByRole('button', {name: 'close selected events panel'});
    fireEvent.click(closeButton);

    expect(defaultContext.clearSelectedFieldEvents).toHaveBeenCalledTimes(1);
  });

  it('should navigate to stages and not navigate to current stage', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1'],
      [StageTypes.TILLAGE_EVENTS]: ['2'],
      [StageTypes.NUTRIENT_EVENTS]: ['3'],
      [StageTypes.IRRIGATION_EVENTS]: ['4'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    const cropButton = screen.getByText('Crops (1)');
    fireEvent.click(cropButton);
    expect(mockNavigateToStage).toHaveBeenCalledWith(mockStages[0]?.type, mockStages[0]?.id);

    const tillageButton = screen.getByText('Tillage (1)');
    fireEvent.click(tillageButton);
    expect(mockNavigateToStage).toHaveBeenCalledWith(mockStages[1]?.type, mockStages[1]?.id);

    const nutrientButton = screen.getByText('Nutrients (1)');
    fireEvent.click(nutrientButton);
    expect(mockNavigateToStage).toHaveBeenCalledWith(mockStages[2]?.type, mockStages[2]?.id);

    // Irrigation is currentStage and should not be clickable
    const irrigationButton = screen.getByText('Irrigation (1)');
    fireEvent.click(irrigationButton);
    expect(mockNavigateToStage).not.toHaveBeenCalledWith(mockStages[3]?.type, mockStages[3]?.id);
  });

  it('should navigate to the selected field', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    const fieldNameButton = screen.getByText(field.name);
    fireEvent.click(fieldNameButton);

    expect(defaultContext.setSelectedField).toHaveBeenCalledWith(field.id);
  });

  it('should enable copy button when there are selected events and open the dialog on click', () => {
    const selectedFieldEvents = createMockSelectedFieldEvents({
      [StageTypes.CROP_EVENTS]: ['1'],
    });

    test.useSelectedFieldContext.mockReturnValue({
      ...defaultContext,
      selectedFieldEvents,
    });

    renderWithProviders(<SelectedEventsSnackbar />);

    const copyfieldEventsBtn = screen.getByRole('button', {name: /copy to/i});
    expect(copyfieldEventsBtn).toBeEnabled();
    fireEvent.click(copyfieldEventsBtn);

    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });
});