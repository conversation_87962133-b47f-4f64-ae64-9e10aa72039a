import React from 'react';

import {Alert, AlertTitle, SvgIcon} from '@regrow-internal/design-system';

import {useNotificationContext} from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import type { NotificationLevel } from 'views/mrv/project/phases/stages/notifications/types';

export const PhaseAlert: React.FC = () => {
  const {phaseNotification, clearPhaseNotification} = useNotificationContext();

  const {error, warning, success} = phaseNotification;

  const shouldShowAlerts = Object.keys(phaseNotification).length > 0;

  const handleClear = (level: NotificationLevel) => {
    clearPhaseNotification(level);
  };

  if (shouldShowAlerts) {
    return (
      <>
        {error?.length
          ? error.map(({title, message}, index) => (
              <Alert key={index} severity={'error'} icon={<SvgIcon type="cross-circled" />} onClose={}>
                <AlertTitle>{title}</AlertTitle>
                {message}
              </Alert>
            ))
          : null}

        {warning?.length
          ? warning.map(({title, message}, index) => (
              <Alert key={index} severity={'warning'} icon={<SvgIcon type="warning-triangled" />}>
                <AlertTitle>{title}</AlertTitle>
                {message}
              </Alert>
            ))
          : null}

        {success?.length
          ? success.map(({title, message}, index) => (
              <Alert key={index} severity={'success'} icon={<SvgIcon type="check-mark-circled" />}>
                <AlertTitle>{title}</AlertTitle>
                {message}
              </Alert>
            ))
          : null}
      </>
    );
  }

  return null;
};
