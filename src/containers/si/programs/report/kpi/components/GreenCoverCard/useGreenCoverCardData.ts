import pick from 'lodash/pick';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {GREENNESSLEVEL_LABEL_MAP} from 'containers/si/programs/content/kpi.greenness_level_area_m2.content';
import {
  GREENNESSLEVEL_FORMATTER_MAP,
  GREENNESSLEVEL_UNIT_MAP,
  makeGreennessLevelMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.greenness_level_area_m2.transformation';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useSubTypeStackedOverTimeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubTypeStackedOverTimeBarChartContent';
import type {SubTypeStackedOverTimeBarChartProps} from 'containers/si/programs/report/kpi/types';

export type UseGreenCoverCardData = {
  isLoading: boolean;
  data: {
    greenCoverTypeOverTimeBarChartContent: SubTypeStackedOverTimeBarChartProps | null;
  };
};

const KPI = 'greenness_level_area_m2';
const FORMATTER_MAP = GREENNESSLEVEL_FORMATTER_MAP;
const UNIT_MAP = GREENNESSLEVEL_UNIT_MAP;

const BY_SUBTYPE_LABEL_MAP = pick(GREENNESSLEVEL_LABEL_MAP, [
  'highArea',
  'moderateArea',
  'lowArea',
  'bareArea',
  'unknownArea',
  'notApplicableArea',
]);

export const useGreenCoverCardData = (): UseGreenCoverCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);
  const kpiTransformer = useMemo(
    () => makeGreennessLevelMetrics(userUnitsSystem),
    [userUnitsSystem]
  );

  const {isLoading, annualizedSummary: byYearMetricsPairs} = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: ['annualized'],
    commonFilters,
    kpiTransformer,
  });

  // all metrics (defined by labelMap keys) must have the same unitDetail and formatter
  const greenCoverTypeOverTimeBarChartContent = useSubTypeStackedOverTimeBarChartContent({
    byYearMetricsPairs,
    filtersState,
    formatter: FORMATTER_MAP['highArea'],
    labelMap: BY_SUBTYPE_LABEL_MAP,
    unitDetail: UNIT_MAP['highArea'](userUnitsSystem),
  });

  const data = {
    greenCoverTypeOverTimeBarChartContent,
  };

  return {isLoading, data};
};
