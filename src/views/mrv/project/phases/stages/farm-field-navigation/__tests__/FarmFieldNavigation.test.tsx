import {fireEvent, screen, within} from '@testing-library/react';
import React from 'react';

import {MockIntersectionObserver} from '_common/test_utils/mockIntersectionObserver';
import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {mockProjectFarms} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {FarmFieldNavigation} from 'views/mrv/project/phases/stages/farm-field-navigation/FarmFieldNavigation';
import {useNotificationContext} from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';

jest.mock('views/mrv/project/phases/stages/notifications/NotificationContext', () => ({
  ...jest.requireActual('views/mrv/project/phases/stages/notifications/NotificationContext'),
  useNotificationContext: jest.fn(),
}));

const mockuseNotificationContext = useNotificationContext as jest.MockedFunction<
  typeof useNotificationContext
>;

Element.prototype.scrollIntoView = jest.fn();
window.IntersectionObserver = MockIntersectionObserver;
global.IntersectionObserver = MockIntersectionObserver;

const defaultState = {
  fieldNotifications: {},
  setNotificationForField: jest.fn(),
  setNotificationForFields: jest.fn(),
  clearNotificationForField: jest.fn(),
  phaseNotification: {},
  setPhaseNotification: jest.fn(),
  clearphaseNotification: jest.fn(),
};

describe('FarmFieldNavigation', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock for field notifications
    mockuseNotificationContext.mockReturnValue(defaultState);
  });

  it('should render loading state', () => {
    renderWithSimpleProviders(
      <FarmFieldNavigation
        farms={[]}
        onFieldClick={jest.fn()}
        selectedFieldId={null}
        isLoading={true}
        fieldCompletionLookup={{}}
      />
    );
    expect(
      screen.queryByRole('navigation', {name: 'field events navigation loading'})
    ).toBeInTheDocument();
  });

  it('should render the farm field navigation sidebar with selected field styles', () => {
    const selectedField = mockProjectFarms[0]?.fields[1];
    renderWithSimpleProviders(
      <FarmFieldNavigation
        farms={mockProjectFarms}
        onFieldClick={jest.fn()}
        selectedFieldId={selectedField?.id as string}
        isLoading={false}
        fieldCompletionLookup={{}}
      />
    );
    expect(screen.queryByRole('navigation', {name: 'Farm field navigation'})).toBeInTheDocument();
    mockProjectFarms.forEach(farm => {
      if (!!farm.fields.length) {
        expect(screen.getByRole('button', {name: farm.name})).toBeInTheDocument();
        farm.fields.forEach(field => {
          const currentField = screen.getByRole('button', {name: field.name});
          expect(currentField).toBeInTheDocument();
          expect(currentField).toHaveAttribute(
            'aria-current',
            (selectedField?.id === field.id).toString()
          );
        });
      }
    });
  });

  it('should allow for toggling the selected field', () => {
    const onFieldClick = jest.fn();
    renderWithSimpleProviders(
      <FarmFieldNavigation
        farms={mockProjectFarms}
        onFieldClick={onFieldClick}
        selectedFieldId={mockProjectFarms[0]?.fields[0]?.id as string}
        fieldCompletionLookup={{}}
      />
    );

    const fieldToSelect = mockProjectFarms[0]?.fields[1];
    fireEvent.click(screen.getByRole('button', {name: fieldToSelect?.name}));
    expect(onFieldClick).toHaveBeenCalledWith(fieldToSelect?.id);
  });

  it('should render checkmark if field is completed', () => {
    const onFieldClick = jest.fn();
    const completedField = mockProjectFarms[0]?.fields[0];
    const incompleteField = mockProjectFarms[0]?.fields[1];

    renderWithSimpleProviders(
      <FarmFieldNavigation
        farms={mockProjectFarms}
        onFieldClick={onFieldClick}
        selectedFieldId={completedField?.id as string}
        fieldCompletionLookup={{
          [String(completedField?.id)]: true,
          [String(incompleteField?.id)]: false,
        }}
      />
    );

    const fieldButton = within(screen.getByRole('button', {name: completedField?.name}));
    expect(fieldButton.getByLabelText(`${completedField?.name} is complete`)).toBeInTheDocument();

    const incompleteFieldButton = within(screen.getByRole('button', {name: incompleteField?.name}));
    expect(
      incompleteFieldButton.queryByLabelText(`${incompleteField?.name} is complete`)
    ).not.toBeInTheDocument();
  });
  it('should allow searching fields by name and by id', () => {
    const [fieldA, fieldB] = mockProjectFarms[0]?.fields ?? [];
    const onFieldClick = jest.fn();

    renderWithSimpleProviders(
      <FarmFieldNavigation
        farms={mockProjectFarms}
        onFieldClick={onFieldClick}
        selectedFieldId={fieldB?.id as string}
        fieldCompletionLookup={{}}
      />
    );

    const searchInput = screen.getByRole('textbox', {name: 'Search'});

    // Search by field name
    fireEvent.change(searchInput, {target: {value: fieldB?.name}});
    expect(screen.getByText(fieldB?.name as string)).toBeInTheDocument();
    expect(screen.queryByText(fieldA?.name as string)).not.toBeInTheDocument();

    // Search by field id
    fireEvent.change(searchInput, {target: {value: fieldA?.id}});
    expect(screen.getByText(fieldA?.name as string)).toBeInTheDocument();
    expect(screen.queryByText(fieldB?.name as string)).not.toBeInTheDocument();
  });

  describe('Field Notifications', () => {
    it('should display notification indicator when field has notifications', () => {
      const fieldId = mockProjectFarms[0]?.fields[0]?.id as string;

      mockuseNotificationContext.mockReturnValue({
        ...defaultState,
        fieldNotifications: {
          [fieldId]: {
            [NotificationLevel.SUCCESS]: ['Success message'],
          },
        },
      });

      renderWithSimpleProviders(
        <FarmFieldNavigation
          farms={mockProjectFarms}
          onFieldClick={jest.fn()}
          selectedFieldId={null}
          isLoading={false}
          fieldCompletionLookup={{}}
        />
      );

      // Should display notification indicator
      expect(screen.getByLabelText('success')).toBeInTheDocument();
    });

    it('should prioritize notification indicator over completion checkmark', () => {
      const {id, name} = mockProjectFarms[0]?.fields[0] as any;

      mockuseNotificationContext.mockReturnValue({
        ...defaultState,
        fieldNotifications: {
          [id]: {
            [NotificationLevel.SUCCESS]: ['Success message'],
          },
        },
      });

      renderWithSimpleProviders(
        <FarmFieldNavigation
          farms={mockProjectFarms}
          onFieldClick={jest.fn()}
          selectedFieldId={null}
          isLoading={false}
          fieldCompletionLookup={{[id]: true}}
        />
      );

      expect(screen.queryByLabelText(`${name} is complete`)).not.toBeInTheDocument();
      expect(screen.queryByLabelText('success')).toBeInTheDocument();
    });

    it('should display error notification with highest priority', () => {
      const fieldId = mockProjectFarms[0]?.fields[0]?.id as string;

      mockuseNotificationContext.mockReturnValue({
        ...defaultState,
        fieldNotifications: {
          [fieldId]: {
            [NotificationLevel.SUCCESS]: ['Success message'],
            [NotificationLevel.WARNING]: ['Warning message'],
            [NotificationLevel.ERROR]: ['Error message'],
          },
        },
      });

      renderWithSimpleProviders(
        <FarmFieldNavigation
          farms={mockProjectFarms}
          onFieldClick={jest.fn()}
          selectedFieldId={null}
          isLoading={false}
          fieldCompletionLookup={{}}
        />
      );

      // Should display error notification (highest priority)
      expect(screen.getByLabelText('error')).toBeInTheDocument();
      expect(screen.queryByLabelText('warning')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('success')).not.toBeInTheDocument();
    });

    it('should not display notification indicator when field has no notifications', () => {
      renderWithSimpleProviders(
        <FarmFieldNavigation
          farms={mockProjectFarms}
          onFieldClick={jest.fn()}
          selectedFieldId={null}
          isLoading={false}
          fieldCompletionLookup={{}}
        />
      );

      // Should not display any notification indicators
      expect(screen.queryByLabelText('success')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('warning')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('error')).not.toBeInTheDocument();
    });

    it('should display notifications for multiple fields', () => {
      const field1Id = mockProjectFarms[0]?.fields[0]?.id as string;
      const field2Id = mockProjectFarms[0]?.fields[1]?.id as string;

      mockuseNotificationContext.mockReturnValue({
        ...defaultState,
        fieldNotifications: {
          [field1Id]: {
            [NotificationLevel.SUCCESS]: ['Success message'],
          },
          [field2Id]: {
            [NotificationLevel.ERROR]: ['Error message'],
          },
        },
      });

      renderWithSimpleProviders(
        <FarmFieldNavigation
          farms={mockProjectFarms}
          onFieldClick={jest.fn()}
          selectedFieldId={null}
          isLoading={false}
          fieldCompletionLookup={{}}
        />
      );

      // Should display both notification indicators
      expect(screen.getByLabelText('success')).toBeInTheDocument();
      expect(screen.getByLabelText('error')).toBeInTheDocument();
    });
  });
});
