import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {isNil} from '_common/utils/typeGuards';

import {
  CROPLAND_NOTAPPLICABLE_TEXT,
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
} from 'containers/si/constants';
import {getUnit} from 'containers/si/utils/convert';

export const COVER_CROP_CONTENT = {
  name: 'cover cropping',
  // TODO: SI-3446 update explainer value to use inclusive if inclusive is rolled out
  explainer:
    (panelLabel: string) =>
    (userUnitsSystem: MeasurementEnum, isCoverCropNaInclusiveEnabled: boolean | undefined) => {
      if (isNil(userUnitsSystem)) return [];

      return isCoverCropNaInclusiveEnabled
        ? COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_INCLUSIVE({panelLabel, userUnitsSystem})
        : COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_EXCLUSIVE({panelLabel, userUnitsSystem});
    },
  categoryColor: '1' as const,
};

// TODO: SI-3446 delete this constant and update explainer value above to use this value instead if inclusive is rolled out.
export const COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_INCLUSIVE = ({
  panelLabel,
  userUnitsSystem,
}: {
  userUnitsSystem: MeasurementEnum;
  panelLabel: string;
}) => [
  `The percentage of cropland practicing cover cropping broken down ${panelLabel}.`,
  `The percentage adoption is based on ${CROPLAND_SATELLITE_VERIFIED_TEXT} ${
    getUnit(userUnitsSystem).unitName.plural
  } only (${
    getUnit(userUnitsSystem).unitName.plural
  } that we can confidently determine cover cropping on fields through remote-sensing). This includes "${CROPLAND_NOTAPPLICABLE_TEXT}" ${
    getUnit(userUnitsSystem).unitName.plural
  } (${
    getUnit(userUnitsSystem).unitName.plural
  } where green cover is classified as perennials, growth of weeds, or volunteer crops).`,
  `Adoption is not determined on ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ${
    getUnit(userUnitsSystem).unitName.plural
  }.`,
];

// TODO: SI-3446 delete this constant
export const COVER_CROP_CONTENT_EXPLAINER_NOT_APPLICABLE_EXCLUSIVE = ({
  panelLabel,
  userUnitsSystem,
}: {
  userUnitsSystem: MeasurementEnum;
  panelLabel: string;
}) => [
  `The percentage of cropland practicing cover cropping broken down ${panelLabel}.`,
  `The percentage adoption is based on ${CROPLAND_SATELLITE_VERIFIED_TEXT} ${
    getUnit(userUnitsSystem).unitName.plural
  } only (${
    getUnit(userUnitsSystem).unitName.plural
  } that we can confidently determine cover cropping on fields through remote-sensing). Adoption is not determined on ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ${
    getUnit(userUnitsSystem).unitName.plural
  } or ${CROPLAND_NOTAPPLICABLE_TEXT} ${getUnit(userUnitsSystem).unitName.plural} (${
    getUnit(userUnitsSystem).unitName.plural
  } where green cover is classified as perennials, growth of weeds, or volunteer crops).`,
];
