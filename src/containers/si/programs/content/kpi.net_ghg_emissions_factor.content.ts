import {CH4, N2O} from 'containers/si/constants';

export const NET_GHG_EF_CONTENT = {
  name: 'Field net emission factor',
  explainer: (panelLabel: string) => [
    `The average amount of field-based ${N2O} and ${CH4} emissions produced and SOC sequestered per kg yield broken down ${panelLabel}.`,
    `Note that standard error can only be calculated by commodity and will only be provided when a single commodity is selected.`,
  ],
  categoryColor: '2' as const,
};
