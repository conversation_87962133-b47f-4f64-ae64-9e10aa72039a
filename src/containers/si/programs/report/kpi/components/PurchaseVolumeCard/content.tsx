import {capitalizeFirstLetter} from '_common/utils/string';

import {PURCHASE_VOLUME_CONTENT} from 'containers/si/programs/content/kpi.purchase_volume_kg.content';
import {
  makePurchaseVolumeMetrics,
  PURCHASEVOLUME_FORMATTER_MAP,
  PURCHASEVOLUMEMETRICS_UNIT_MAP,
  type PurchaseVolumeKgMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.purchase_volume_kg.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const KPI = 'purchase_volume_kg';
export const FORMATTER_MAP = PURCHASEVOLUME_FORMATTER_MAP;
export const UNIT_MAP = PURCHASEVOLUMEMETRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<PurchaseVolumeKgMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'volumePurchased',
};
export const CATEGORY_COLOR_KEY = PURCHASE_VOLUME_CONTENT.categoryColor;
export const TRANSFORMER = makePurchaseVolumeMetrics;

export const CARD_CONTENT = {
  title: capitalizeFirstLetter(PURCHASE_VOLUME_CONTENT.name),
  isDownwardChangePositive: false,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: PURCHASE_VOLUME_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: PURCHASE_VOLUME_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: PURCHASE_VOLUME_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
