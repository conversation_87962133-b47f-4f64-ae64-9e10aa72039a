export enum NotificationLevel {
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
}

export type NotificationContent = {
  id: string;
  title: string;
  message: string;
};

export type NotificationLookup = {
  [NotificationLevel.SUCCESS]?: Array<NotificationContent>;
  [NotificationLevel.WARNING]?: Array<NotificationContent>;
  [NotificationLevel.ERROR]?: Array<NotificationContent>;
};

export type FieldNotifications = {
  [fieldId: string]: NotificationLookup;
};