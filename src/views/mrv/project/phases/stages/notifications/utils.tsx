import type { FieldNotifications, NotificationLevel, NotificationLookup } from "./types";

export const clearFieldNotificationState = (previousState: FieldNotifications, fieldId: string, level?: NotificationLevel, notificationId?: string) => {
    const previousFieldState = previousState[fieldId];

    if (!previousFieldState) {
      return previousState;
    }

    if (level) {
      const levelState = previousFieldState[level];

      if (!levelState) {
        return previousState;
      }

      if (!notificationId) {
        // Clear all notifications for this level
        const updatedFieldState = {
          ...previousFieldState,
        };
        delete updatedFieldState[level];

        // If no notifications remain for this field, remove the field entirely
        if (Object.keys(updatedFieldState).length === 0) {
          const newState = {...previousState};
          delete newState[fieldId];
          return newState;
        }

        return {
          ...previousState,
          [fieldId]: updatedFieldState,
        };
      }

      // Clear specific notification by ID
      const newState = levelState.filter(n => n.id !== notificationId);

      if (newState.length === 0) {
        // No notifications left for this level, remove the level
        const updatedFieldState = {
          ...previousFieldState,
        };
        delete updatedFieldState[level];

        // If no notifications remain for this field, remove the field entirely
        if (Object.keys(updatedFieldState).length === 0) {
          const newStateWithoutField = {...previousState};
          delete newStateWithoutField[fieldId];
          return newStateWithoutField;
        }

        return {
          ...previousState,
          [fieldId]: updatedFieldState,
        };
      }

      return {
        ...previousState,
        [fieldId]: {
          ...previousFieldState,
          [level]: newState,
        },
      };
    } else {
      // Clear all notifications for this field
      const newState = {...previousState};
      delete newState[fieldId];
      return newState;
    }
  };

  
  export const clearPhaseNotificationState = (previousState: NotificationLookup, level?: NotificationLevel, notificationId?: string) => {
    if (level) {
      const levelState = previousState[level];
  
      if (!levelState) {
        return previousState;
      }
  
      if (!notificationId) {
        // Clear all notifications for this level
        const updatedState = {
          ...previousState,
        };
        delete updatedState[level];
        return updatedState;
      }

      // Clear specific notification by ID
      const newState = levelState.filter(n => n.id !== notificationId);
  
      if (newState.length === 0) {
        // No notifications left for this level, remove the level
        const updatedState = {
          ...previousState,
        };
        delete updatedState[level];
        return updatedState;
      }
  
      return {
        ...previousState,
        [level]: newState,
      };
    } else {
      // Clear all notifications
      return {};
    }
  };
