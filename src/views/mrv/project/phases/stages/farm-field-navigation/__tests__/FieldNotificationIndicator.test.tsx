import {screen} from '@testing-library/react';
import React from 'react';

import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {FieldNotificationIndicator} from 'views/mrv/project/phases/stages/farm-field-navigation/FieldNotificationIndicator';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';

describe('FieldNotificationIndicator', () => {
  it('should render nothing when no notifications are provided', () => {
    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={null} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should render nothing when notifications object is empty', () => {
    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={{}} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should render success indicator when only success notifications exist', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [
        {
          id: 'success-1',
          title: 'Success',
          message: 'Success message',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('success')).toBeInTheDocument();
  });

  it('should render warning indicator when only warning notifications exist', () => {
    const notifications = {
      [NotificationLevel.WARNING]: [
        {
          id: 'warning-1',
          title: 'Warning',
          message: 'Warning message',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should render error indicator when only error notifications exist', () => {
    const notifications = {
      [NotificationLevel.ERROR]: [
        {
          id: 'error-1',
          title: 'Error',
          message: 'Error message',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('error')).toBeInTheDocument();
  });

  it('should prioritize error over warning and success', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [
        {
          id: 'success-1',
          title: 'Success',
          message: 'Success message',
        },
      ],
      [NotificationLevel.WARNING]: [
        {
          id: 'warning-1',
          title: 'Warning',
          message: 'Warning message',
        },
      ],
      [NotificationLevel.ERROR]: [
        {
          id: 'error-1',
          title: 'Error',
          message: 'Error message',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('error')).toBeInTheDocument();
  });

  it('should prioritize warning over success when no error exists', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [
        {
          id: 'success-1',
          title: 'Success',
          message: 'Success message',
        },
      ],
      [NotificationLevel.WARNING]: [
        {
          id: 'warning-1',
          title: 'Warning',
          message: 'Warning message',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should render nothing when notification arrays are empty', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [],
      [NotificationLevel.WARNING]: [],
      [NotificationLevel.ERROR]: [],
    };

    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={notifications} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('should handle multiple messages of the same level', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [
        {
          id: 'success-1',
          title: 'Success 1',
          message: 'First success',
        },
        {
          id: 'success-2',
          title: 'Success 2',
          message: 'Second success',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('success')).toBeInTheDocument();
  });

  it('should handle undefined notification arrays gracefully', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: undefined,
      [NotificationLevel.WARNING]: [
        {
          id: 'warning-1',
          title: 'Warning',
          message: 'Warning message',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should handle mixed empty and populated arrays', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [],
      [NotificationLevel.WARNING]: [
        {
          id: 'warning-1',
          title: 'Warning',
          message: 'Warning message',
        },
      ],
      [NotificationLevel.ERROR]: [],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByLabelText('warning')).toBeInTheDocument();
  });

  it('should handle multiple messages across different levels and prioritize correctly', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: [
        {
          id: 'success-1',
          title: 'Success 1',
          message: 'Success 1',
        },
        {
          id: 'success-2',
          title: 'Success 2',
          message: 'Success 2',
        },
      ],
      [NotificationLevel.WARNING]: [
        {
          id: 'warning-1',
          title: 'Warning 1',
          message: 'Warning 1',
        },
        {
          id: 'warning-2',
          title: 'Warning 2',
          message: 'Warning 2',
        },
      ],
      [NotificationLevel.ERROR]: [
        {
          id: 'error-1',
          title: 'Error 1',
          message: 'Error 1',
        },
        {
          id: 'error-2',
          title: 'Error 2',
          message: 'Error 2',
        },
      ],
    };

    renderWithSimpleProviders(<FieldNotificationIndicator notifications={notifications} />);

    const indicator = screen.getByText('●');
    expect(indicator).toBeInTheDocument();
    // Should still prioritize error even with multiple messages
    expect(screen.getByLabelText('error')).toBeInTheDocument();
  });

  it('should render nothing when all notification arrays are explicitly undefined', () => {
    const notifications = {
      [NotificationLevel.SUCCESS]: undefined,
      [NotificationLevel.WARNING]: undefined,
      [NotificationLevel.ERROR]: undefined,
    };

    const {container} = renderWithSimpleProviders(
      <FieldNotificationIndicator notifications={notifications} />
    );
    expect(container.firstChild).toBeNull();
  });
});
