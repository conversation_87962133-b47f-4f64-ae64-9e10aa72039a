import {screen, waitFor} from '@testing-library/react';
import React from 'react';

import {useAppSelector} from 'store/useRedux';
import {RequestStatus} from 'types';

import {MonitorTaskStatus} from '_common/api/integrations/monitor';
import {renderWithProviders} from '_common/test_utils/renderWithProviders';

import {MonitorPrefillAlert} from 'views/mrv/project/phases/stages/notifications/alerts/MonitorPrefillAlert';

jest.mock('store/useRedux', () => ({
  ...jest.requireActual('store/useRedux'),
  useAppSelector: jest.fn(),
}));

(useAppSelector as jest.Mock).mockImplementation(selector =>
  selector({
    integrations: {
      platforms: {
        monitor: {
          syncStatus: 'Idle',
          error: null,
          prefilledFields: [],
        },
      },
    },
  })
);

describe('MonitorPrefillAlert', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading alert when monitorImportStatus is Loading', () => {
    (useAppSelector as jest.Mock).mockReturnValue({
      syncStatus: RequestStatus.Loading,
      prefilledFields: [],
    });

    renderWithProviders(<MonitorPrefillAlert />);

    expect(screen.getByText('Detecting crop and tillage information.')).toBeInTheDocument();
    expect(
      screen.getByText('Remotely sensed data is being added to your cropping and tillage history.')
    ).toBeInTheDocument();
  });

  it('renders success alert when monitorImportIsComplete is true', async () => {
    (useAppSelector as jest.Mock).mockReturnValue({
      syncStatus: RequestStatus.Loading,
      prefilledFields: [{md5: '123', status: MonitorTaskStatus.Succeeded}],
    });

    renderWithProviders(<MonitorPrefillAlert />);

    await waitFor(() => {
      expect(
        screen.getByText('Your crop and tillage information was successfully added!')
      ).toBeInTheDocument();
    });
  });

  it('does not render alert when monitorImportStatus is not Loading or Success', () => {
    (useAppSelector as jest.Mock).mockReturnValue({
      syncStatus: RequestStatus.Error,
      prefilledFields: [],
    });

    renderWithProviders(<MonitorPrefillAlert />);

    expect(screen.queryByText('Detecting crop and tillage information.')).not.toBeInTheDocument();
    expect(
      screen.queryByText('Your crop and tillage information was successfully added!')
    ).not.toBeInTheDocument();
  });

  it('shows percentage complete when fieldImportPercentageComplete is not null', () => {
    (useAppSelector as jest.Mock).mockReturnValue({
      syncStatus: RequestStatus.Loading,
      prefilledFields: [{md5: '123', status: MonitorTaskStatus.Processing}],
    });

    renderWithProviders(<MonitorPrefillAlert />);

    expect(screen.getByText(/0%\s*Complete/i)).toBeInTheDocument();
  });

  it('shows percentage complete when fieldImportPercentageComplete is not null and 50%', async () => {
    (useAppSelector as jest.Mock).mockReturnValue({
      syncStatus: RequestStatus.Loading,
      prefilledFields: [
        {md5: '123', status: MonitorTaskStatus.Processing},
        {md5: '1235', status: MonitorTaskStatus.Succeeded},
      ],
    });

    renderWithProviders(<MonitorPrefillAlert />);
    await waitFor(() => {
      expect(screen.getByText(/50%\s*Complete/i)).toBeInTheDocument();
    });
  });
});
