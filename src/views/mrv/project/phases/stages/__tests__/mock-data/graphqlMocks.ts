import {AttributeTypes, IrrigationMethods, StageTypes} from '__generated__/mrv/mrvApi.types';

import {
  CREATE_OR_UPDATE_FIELD_EVENT,
  DELETE_FIELD_EVENT,
  UPDATE_NO_PRACTICE_OBSERVATION,
} from 'views/mrv/graphql/mutations/field-events';
import {
  GET_FIELD_CULTIVATION_CYCLE_EVENTS,
  GET_PHASE_CULTIVATION_CYCLE_EVENTS,
} from 'views/mrv/graphql/queries/field-events';
import {GET_PROJECT_FARMS_AND_ENROLLED_FIELDS} from 'views/mrv/graphql/queries/projects';
import {GET_STAGE_ATTRIBUTES, GET_STAGE_COMPLETION} from 'views/mrv/graphql/queries/stages';
import {mockCropEventsStage} from 'views/mrv/project/phases/__tests__/graphqlMocks';

import {
  attributeOptions,
  croppingEvents,
  croppingEventsLocked,
  fieldsWithCultivationCycles,
  getFieldWithEvents,
  getProjectFields,
  irrigationEvents,
  mockDeleteFieldEventRowModelFiltered,
  mockFieldId,
  mockProjectFarms,
  mockSelectedField,
  tillageEvents,
} from './mocks';

const mockTillageEventsStage = {
  name: 'Tillage',
  type: 'TILLAGE_EVENTS',
  custom_name: 'Tillage events',
  description: 'Hello World',
  locked: false,
  enabled: true,
  order: 1,
  required: false,
  optis_prefill: false,
  icon: '/static/stages/historical_crop_rotation.svg',
};

export const GET_FARMS_AND_ENROLLED_FIELDS_MOCK = {
  request: {
    query: GET_PROJECT_FARMS_AND_ENROLLED_FIELDS,
    variables: {projectId: '1'},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '14989',
          farms: mockProjectFarms,
        },
      },
    },
  },
};

export const ERROR_FARMS_AND_FIELDS_MOCK = {
  ...GET_FARMS_AND_ENROLLED_FIELDS_MOCK,
  error: new Error('An error occurred'),
  result: undefined,
};

export const GET_PHASE_CULTIVATION_CYCLE_EVENTS_LOCKED_MOCK = {
  request: {
    query: GET_PHASE_CULTIVATION_CYCLE_EVENTS,
    variables: {projectId: '1', stageId: mockCropEventsStage.id},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '14989',
          stage: {
            id: mockCropEventsStage.id,
            fields: fieldsWithCultivationCycles(croppingEventsLocked),
          },
        },
      },
    },
  },
};

export const GET_CROP_STAGE_ATTRIBUTES_MOCK = {
  request: {
    query: GET_STAGE_ATTRIBUTES,
    variables: {projectId: '1', stageId: mockCropEventsStage.id},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '14989',
          stage: {
            ...mockCropEventsStage,
            attribute_options: attributeOptions,
          },
        },
      },
    },
  },
};

export const GET_STAGE_COMPLETION_MOCK = {
  request: {
    query: GET_STAGE_COMPLETION,
    variables: {projectId: '1', stageId: mockCropEventsStage.id},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '14989',
          stage: {
            id: mockCropEventsStage.id,
            type: StageTypes.CROP_EVENTS,
            fields: getProjectFields(),
          },
        },
      },
    },
  },
};

const irrigationStageId = '14989';
export const GET_IRRIGATION_STAGE_ATTRIBUTES_MOCK = {
  request: {
    query: GET_STAGE_ATTRIBUTES,
    variables: {projectId: '1', stageId: irrigationStageId},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: irrigationStageId,
          stage: {
            id: irrigationStageId,
            type: StageTypes.IRRIGATION_EVENTS,
            attribute_options: {...attributeOptions, application_product: null},
            locked: false,
            enabled: true,
            required: true,
            name: 'Irrigation Stage',
          },
        },
      },
    },
  },
};

const irrigationResult = {
  data: {
    mrv: {
      project: {
        id: '1',
        stage: {
          id: irrigationStageId,
          fields: fieldsWithCultivationCycles(irrigationEvents),
        },
      },
    },
  },
};

export const getFieldCultivationCycleEventsMock = (
  fieldId = mockFieldId,
  stageId = irrigationStageId,
  events = irrigationEvents
) => ({
  request: {
    query: GET_FIELD_CULTIVATION_CYCLE_EVENTS,
    variables: {
      fieldId,
      projectId: '1',
      stageId,
      prefill_monitoring_phase: false,
    },
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '1',
          stage: {
            id: stageId,
            field: {
              ...getFieldWithEvents(events),
              id: fieldId,
            },
          },
        },
      },
    },
  },
  newData: jest.fn(() => irrigationResult),
});

export const GET_FIELD_CULTIVATION_CYCLE_EVENTS_MOCK_ERROR = {
  ...getFieldCultivationCycleEventsMock(mockSelectedField?.id),
  error: new Error('An error occurred'),
  result: undefined,
};

const tillageStageId = '41294';
export const GET_TILLAGE_STAGE_ATTRIBUTES_MOCK = {
  request: {
    query: GET_STAGE_ATTRIBUTES,
    variables: {projectId: '1', stageId: tillageStageId},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '14989',
          stage: {
            ...mockTillageEventsStage,
            id: tillageStageId,
            attribute_options: {...attributeOptions, application_product: null},
          },
        },
      },
    },
  },
};
const nutrientsStageId = '41244';
export const GET_NUTRIENT_STAGE_ATTRIBUTES_MOCK = {
  request: {
    query: GET_STAGE_ATTRIBUTES,
    variables: {projectId: '1', stageId: nutrientsStageId},
  },
  result: {
    data: {
      mrv: {
        project: {
          id: '14989',
          stage: {
            id: nutrientsStageId,
            type: StageTypes.NUTRIENT_EVENTS,
            attribute_options: attributeOptions,
            name: 'Nutrient Stage',
            locked: false,
            enabled: true,
            required: true,
          },
        },
      },
    },
  },
};

export const UPDATE_FIELD_EVENT_MOCK = {
  request: {
    query: CREATE_OR_UPDATE_FIELD_EVENT,
    variables: {
      fieldId: mockFieldId,
      phaseId: 'undefined',
      projectId: '1',
      event: {
        id: 'eventId',
        type: 'IrrigationEvent',
        event_values: {
          irrigation_method: IrrigationMethods.Furrow,
          start_date: '2021-01-01',
          end_date: '2021-01-02',
        },
      },
    },
  },
  newData: jest.fn(() => {
    return {
      data: {
        createOrUpdateFieldEvent: {
          id: 'eventId',
          type: 'IrrigationEvent',
          event_values: {irrigation_method: 'Furrow'},
        },
      },
    };
  }),
};

export const CREATE_FIELD_EVENT_MOCK = {
  request: {
    query: CREATE_OR_UPDATE_FIELD_EVENT,
    variables: {
      fieldId: mockFieldId,
      phaseId: 'undefined',
      projectId: '1',
      event: {
        id: null,
        type: 'IrrigationEvent',
        event_values: {
          irrigation_method: IrrigationMethods.Furrow,
          start_date: '2021-01-01',
          end_date: '2021-01-02',
        },
      },
    },
  },
  newData: jest.fn(() => {
    return {
      data: {
        createOrUpdateFieldEvent: {
          id: 'eventId',
          [AttributeTypes.SubsurfaceDripDepthUnit]: 'mm1ha-1',
        },
      },
    };
  }),
};

export const DELETE_FIELD_EVENT_MOCK = {
  request: {
    query: DELETE_FIELD_EVENT,
    variables: {
      projectId: '1',
      phaseId: 'undefined',
      fieldId: mockFieldId,
      event: mockDeleteFieldEventRowModelFiltered,
    },
  },
  newData: jest.fn(() => {
    return {
      data: {
        deleteFieldEvent: true,
      },
    };
  }),
};

const mockCultCycleId =
  '{"crop_event_id":{"start_id":"0dcb9201-1e05-4325-954b-ffe4f477d1af","end_id":"bfd7de15-3881-401a-83c2-37f31966beba"},"crop_type":"peanut","start_date":"2024-03-29T00:00:00+00:00","end_date":"2025-03-28T00:00:00+00:00"}';
export const UPDATE_NO_PRACTICE_OBSERVATION_MOCK = {
  request: {
    query: UPDATE_NO_PRACTICE_OBSERVATION,
    variables: {
      projectId: '1',
      phaseId: 'undefined',
      stageId: '14989',
      fieldId: mockFieldId,
      cultivationCycleId: mockCultCycleId,
      noPracticeObservationValue: true,
    },
  },
  newData: jest.fn(() => {
    return {
      data: {
        updateCultivationCycleNoPracticeObservation: {
          id: mockCultCycleId,
          start_date: '2012-05-12',
          end_date: '2012-12-12',
          name: 'Cultivation cycle name 2',
          crop_type: 'corn',
          no_practice_observations: {
            tillage_event: false,
            irrigation_event: true,
            application_event: false,
          },
          events: [],
        },
      },
    };
  }),
};

const getPhaseResult = (withEvents: boolean) => ({
  mrv: {
    project: {
      id: '1',
      program: {
        id: '1598',
        phases: [
          {
            id: '4632',
            stages: [
              {
                id: '29505',
                type: 'CROP_EVENTS',
                field: {
                  id: '191580',
                  name: 'Field 1',
                  core_farm_group_id: 59131,
                  area: 0.48,
                  geometry: {
                    type: 'MultiPolygon',
                    coordinates: [],
                    __typename: 'FieldGeometry',
                  },
                  cultivation_cycles: [
                    {
                      id: '{"crop_event_id":{"start_id":"9532908b-962a-41d2-a8f5-769ada290276","end_id":"bb2291b1-8f97-4758-9e17-eb96e2e5d26c"},"crop_type":"rye_spring","start_date":"2021-05-21T00:00:00+00:00","end_date":"2022-05-20T00:00:00+00:00","phase_id":4632,"stage_id":29505}',
                      start_date: '2021-05-21T00:00:00+00:00',
                      end_date: '2022-05-20T00:00:00+00:00',
                      name: 'rye_spring 2022',
                      crop_type: 'rye_spring',
                      is_crop_event_locked: false,
                      no_practice_observations: {
                        tillage_event: false,
                        irrigation_event: false,
                        application_event: false,
                        __typename: 'NoPracticeObservations',
                        contains_prefilled_monitoring_phase_events: false,
                      },
                      events: withEvents ? croppingEvents() : [],
                      __typename: 'CultivationCycle',
                    },

                    {
                      id: '{"crop_event_id":{"start_id":"35a19dde-411f-4782-b057-d2ed6ccf0128","end_id":"c018aaee-7fe1-46ba-97ba-063f978138c1"},"crop_type":"rice","start_date":"2023-02-25T00:00:00+00:00","end_date":"2023-06-23T00:00:00+00:00","phase_id":4632,"stage_id":29505}',
                      start_date: '2023-02-25T00:00:00+00:00',
                      end_date: '2023-06-23T00:00:00+00:00',
                      name: 'rice 2023',
                      crop_type: 'rice',
                      no_practice_observations: {
                        tillage_event: false,
                        irrigation_event: false,
                        application_event: false,
                        __typename: 'NoPracticeObservations',
                      },
                      contains_prefilled_monitoring_phase_events: false,

                      events: withEvents ? croppingEvents() : [],
                      __typename: 'CultivationCycle',
                    },
                  ],
                  __typename: 'Field',
                },
                __typename: 'Stage',
              },
              {
                id: '29506',
                type: 'TILLAGE_EVENTS',
                field: {
                  id: '191580',
                  name: 'Field 1',
                  core_farm_group_id: 59131,
                  area: 0.48,
                  geometry: {
                    type: 'MultiPolygon',
                    coordinates: [],
                    __typename: 'FieldGeometry',
                  },
                  cultivation_cycles: [
                    {
                      id: '{"crop_event_id":null,"crop_type":null,"start_date":"2020-01-01T00:00:00+00:00","end_date":"2021-05-20T00:00:00+00:00","phase_id":4632,"stage_id":29506}',
                      start_date: '2020-01-01T00:00:00+00:00',
                      end_date: '2021-05-20T00:00:00+00:00',
                      name: '',
                      crop_type: null,
                      is_crop_event_locked: false,
                      no_practice_observations: {
                        tillage_event: false,
                        irrigation_event: false,
                        application_event: false,
                        __typename: 'NoPracticeObservations',
                        contains_prefilled_monitoring_phase_events: false,
                      },
                      events: withEvents ? tillageEvents() : [],
                      __typename: 'CultivationCycle',
                    },
                    {
                      id: '{"crop_event_id":{"start_id":"9532908b-962a-41d2-a8f5-769ada290276","end_id":"bb2291b1-8f97-4758-9e17-eb96e2e5d26c"},"crop_type":"rye_spring","start_date":"2021-05-21T00:00:00+00:00","end_date":"2022-05-20T00:00:00+00:00","phase_id":4632,"stage_id":29506}',
                      start_date: '2021-05-21T00:00:00+00:00',
                      end_date: '2022-05-20T00:00:00+00:00',
                      name: 'rye_spring 2022',
                      crop_type: 'rye_spring',
                      is_crop_event_locked: false,
                      no_practice_observations: {
                        tillage_event: false,
                        irrigation_event: false,
                        application_event: false,
                        __typename: 'NoPracticeObservations',
                      },
                      contains_prefilled_monitoring_phase_events: false,

                      events: withEvents ? tillageEvents() : [],
                      __typename: 'CultivationCycle',
                    },
                    {
                      id: '{"crop_event_id":{"start_id":"4eb09bb7-0583-4bd2-8fed-534828799f68","end_id":"64aad29a-61f1-485a-add1-94552ab8d054"},"crop_type":"peanut","start_date":"2022-05-21T00:00:00+00:00","end_date":"2022-10-21T00:00:00+00:00","phase_id":4632,"stage_id":29506}',
                      start_date: '2022-05-21T00:00:00+00:00',
                      end_date: '2022-10-21T00:00:00+00:00',
                      name: 'peanut 2022',
                      crop_type: 'peanut',
                      is_crop_event_locked: false,
                      no_practice_observations: {
                        tillage_event: false,
                        irrigation_event: false,
                        application_event: false,
                        __typename: 'NoPracticeObservations',
                      },
                      contains_prefilled_monitoring_phase_events: false,
                      events: [],
                      __typename: 'CultivationCycle',
                    },
                  ],
                  __typename: 'Field',
                },
                __typename: 'Stage',
              },
            ],
            __typename: 'Phase',
          },
        ],
        __typename: 'Program',
      },
      __typename: 'Project',
    },
    __typename: 'MRV',
  },
});

export const GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK = {
  request: {
    query: GET_PHASE_CULTIVATION_CYCLE_EVENTS,
    variables: {
      fieldId: '108264',
      projectId: '1',
      phaseType: 'ENROLMENT',
      prefill_monitoring_phase: false,
    },
  },
  result: {loading: false, data: getPhaseResult(true)},
};

export const GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK = {
  ...GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK,
  result: {loading: false, data: getPhaseResult(false)},
};
