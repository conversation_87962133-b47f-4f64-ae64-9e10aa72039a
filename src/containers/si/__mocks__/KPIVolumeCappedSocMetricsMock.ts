import type {VolumeWeightedSocKgKPIResponse} from 'containers/si/api/apiTypes';

const KPIVolumeCappedSocKgMock: VolumeWeightedSocKgKPIResponse = {
  kpi_name: 'volume_weighted_soc_kg',
  year: 2023,
  metric: {
    volume_weighted_soc_kg: 100,
  },
  units: 'kg',
  boundary_type: 'subsection',
};

export const KPIVolumeCappedSocKgSummarizeByCropTypeMock: VolumeWeightedSocKgKPIResponse &
  Required<Pick<VolumeWeightedSocKgKPIResponse, 'crop_type_summary'>> = {
  ...KPIVolumeCappedSocKgMock,
  crop_type_summary: {
    1: {volume_weighted_soc_kg: 10},
    2: {volume_weighted_soc_kg: 20},
    3: {volume_weighted_soc_kg: 30},
  },
};
