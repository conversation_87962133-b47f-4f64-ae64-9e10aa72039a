import {VOLUME_CAPPED_GHG_CONTENT} from 'containers/si/programs/content/kpi.volume_capped_ghg_kg.content';
import {
  makeVolumeCappedGhgMetrics,
  VOLUME_CAPPED_GHG_METRICS_FORMATTER_MAP,
  VOLUME_CAPPED_GHG_METRICS_UNIT_MAP,
  type VolumeCappedGhgMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_capped_ghg_kg.transformation';
import {
  NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const KPI = 'volume_weighted_ghg_kg';
export const FORMATTER_MAP = VOLUME_CAPPED_GHG_METRICS_FORMATTER_MAP;
export const UNIT_MAP = VOLUME_CAPPED_GHG_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<VolumeCappedGhgMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'volumeCappedGhgMass',
};
export const TRANSFORMER = makeVolumeCappedGhgMetrics;
export const CATEGORY_COLOR_KEY = VOLUME_CAPPED_GHG_CONTENT.categoryColor;

export const CARD_CONTENT = {isDownwardChangePositive: true};

export const PANEL_CONTENT = {
  ByYear: {
    title: VOLUME_CAPPED_GHG_CONTENT.name(PANEL_LABEL.OVER_TIME),
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: VOLUME_CAPPED_GHG_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: VOLUME_CAPPED_GHG_CONTENT.name(PANEL_LABEL.BY_CROP_TYPE),
    label: PANEL_LABEL.BY_CROP_TYPE,
    isDisabledMessage: (hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
    tooltipContent: VOLUME_CAPPED_GHG_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: VOLUME_CAPPED_GHG_CONTENT.name(PANEL_LABEL.BY_AREA),
    label: PANEL_LABEL.BY_AREA,
    isDisabledMessage: (hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
    tooltipContent: VOLUME_CAPPED_GHG_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
