import {isDefined} from '_common/utils/typeGuards';

import {CH4, N2O} from 'containers/si/constants';

export const VOLUME_CAPPED_GHG_CONTENT = {
  name: (panelLabel?: string) =>
    `Total field GHG emissions ${isDefined(panelLabel) ? `${panelLabel} ` : ''}(volume-capped)`,
  explainer: (panelLabel: string) => [
    `The sum of field-based ${N2O} and ${CH4} emissions produced broken down ${panelLabel} specific to the volume of the commodities purchased.`,
    `Note that standard error is not available for volume-based data and that volume-based data is omitted for commodities/subregions/years without volumes.`,
  ],
  categoryColor: '2' as const,
};
