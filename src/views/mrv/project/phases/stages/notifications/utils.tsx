import type {FieldNotifications, NotificationLevel, NotificationLookup} from './types';

const removeFieldFromState = (
  previousState: FieldNotifications,
  fieldId: string
): FieldNotifications => {
  const newState = {...previousState};
  delete newState[fieldId];
  return newState;
};

const removeLevelFromField = (
  previousState: FieldNotifications,
  fieldId: string,
  level: NotificationLevel
): FieldNotifications => {
  const previousFieldState = previousState[fieldId];
  const updatedFieldState = {...previousFieldState};
  delete updatedFieldState[level];

  if (Object.keys(updatedFieldState).length === 0) {
    return removeFieldFromState(previousState, fieldId);
  }

  return {
    ...previousState,
    [fieldId]: updatedFieldState,
  };
};

const removeNotificationById = (
  previousState: FieldNotifications,
  fieldId: string,
  level: NotificationLevel,
  notificationId: string
): FieldNotifications => {
  const previousFieldState = previousState[fieldId];

  if (!previousFieldState) {
    return previousState;
  }

  const levelState = previousFieldState[level];

  if (!levelState) {
    return previousState;
  }

  const filteredNotifications = levelState.filter(n => n.id !== notificationId);

  if (filteredNotifications.length === levelState.length) {
    return previousState;
  }

  if (filteredNotifications.length === 0) {
    return removeLevelFromField(previousState, fieldId, level);
  }

  return {
    ...previousState,
    [fieldId]: {
      ...previousFieldState,
      [level]: filteredNotifications,
    },
  };
};

export const clearFieldNotificationState = (
  previousState: FieldNotifications,
  fieldId: string,
  level?: NotificationLevel,
  notificationId?: string
): FieldNotifications => {
  const previousFieldState = previousState[fieldId];

  if (!previousFieldState) {
    return previousState;
  }

  if (!level) {
    return removeFieldFromState(previousState, fieldId);
  }

  if (notificationId) {
    return removeNotificationById(previousState, fieldId, level, notificationId);
  }

  return removeLevelFromField(previousState, fieldId, level);
};

const removeLevelFromPhase = (
  previousState: NotificationLookup,
  level: NotificationLevel
): NotificationLookup => {
  const updatedState = {...previousState};
  delete updatedState[level];
  return updatedState;
};

const removePhaseNotificationById = (
  previousState: NotificationLookup,
  level: NotificationLevel,
  notificationId: string
): NotificationLookup => {
  const levelState = previousState[level];

  if (!levelState) {
    return previousState;
  }

  const filteredNotifications = levelState.filter(n => n.id !== notificationId);

  if (filteredNotifications.length === 0) {
    return removeLevelFromPhase(previousState, level);
  }

  return {
    ...previousState,
    [level]: filteredNotifications,
  };
};

export const clearPhaseNotificationState = (
  previousState: NotificationLookup,
  level?: NotificationLevel,
  notificationId?: string
): NotificationLookup => {
  if (!level) {
    return {};
  }

  if (notificationId) {
    return removePhaseNotificationById(previousState, level, notificationId);
  }

  return removeLevelFromPhase(previousState, level);
};
