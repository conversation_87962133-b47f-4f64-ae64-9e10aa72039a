import {getTypedKeys} from '_common/utils/object';

import type {DroughtIndexKPIMetricData} from 'containers/si/api/apiTypes';
import {
  isValidTier,
  tierFormatter,
  tierUnit,
} from 'containers/si/programs/helpers/kpi.quantification_level.helpers';
import {isFiniteNumber} from 'containers/si/utils/utils';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type DroughtMetrics = {
  droughtIndex: Metric;
  tier: Metric;
};

// this is done to accommodate rendering a data point above the 0 axes
export const BE_TO_FE_DROUGHT_INDEX_MAP = {
  '-1': 1,
  '0': 2,
  '1': 3,
  '2': 4,
  '3': 5,
  '4': 6,
} as const;

type BEDroughtIndexString = keyof typeof BE_TO_FE_DROUGHT_INDEX_MAP;

const FE_DROUGHT_INDEX_STRING_TO_LABEL_MAP = {
  '1': 'Normal / wet',
  '2': 'D0',
  '3': 'D1',
  '4': 'D2',
  '5': 'D3',
  '6': 'D4',
} as const;

type FEDroughtIndexString = keyof typeof FE_DROUGHT_INDEX_STRING_TO_LABEL_MAP;

export const private__isValidBEDroughtIndexString = (v: unknown): v is BEDroughtIndexString =>
  getTypedKeys(BE_TO_FE_DROUGHT_INDEX_MAP)
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    .includes(v as BEDroughtIndexString);

export const private__isValidFEDroughtIndexString = (v: unknown): v is FEDroughtIndexString =>
  getTypedKeys(FE_DROUGHT_INDEX_STRING_TO_LABEL_MAP)
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    .includes(v as FEDroughtIndexString);

export const private__droughtUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'drought-index' as const,
    unitName: {
      singular: 'classification',
      plural: 'classification',
      abbr: 'classification',
    },
  };
};

export const private__droughtFormatter = (value: number): string => {
  const valueString = String(value);

  // should not encounter this as a validity check is done on drought index in the transformer
  // we should not be do any averaging or math which could yield non-discrete drought values on the FE for drought metrics which could this to happen
  if (!private__isValidFEDroughtIndexString(valueString)) {
    return '';
  }

  return FE_DROUGHT_INDEX_STRING_TO_LABEL_MAP[valueString];
};

export const DROUGHT_METRICS_FORMATTER_MAP: Record<
  keyof DroughtMetrics,
  (value: number) => string
> = {
  droughtIndex: private__droughtFormatter,
  tier: tierFormatter,
};

export const DROUGHT_METRICS_UNIT_MAP: Record<keyof DroughtMetrics, () => UnitDetail<UnitType>> = {
  droughtIndex: private__droughtUnit,
  tier: tierUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeDroughtMetrics);
 */
export const makeDroughtMetrics = (data: DroughtIndexKPIMetricData): DroughtMetrics | null => {
  const {drought_index, quantification_level} = data;

  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (
    !isFiniteNumber(drought_index) ||
    !isFiniteNumber(quantification_level) ||
    !isValidTier(quantification_level)
  ) {
    return null;
  }

  const drought_index_str = String(drought_index);

  if (!private__isValidBEDroughtIndexString(drought_index_str)) return null;

  const droughtIndexValue = BE_TO_FE_DROUGHT_INDEX_MAP[drought_index_str];

  return {
    droughtIndex: {
      value: droughtIndexValue,
      unit: DROUGHT_METRICS_UNIT_MAP.droughtIndex().unit,
      formattedValue: DROUGHT_METRICS_FORMATTER_MAP.droughtIndex(droughtIndexValue),
    },
    tier: {
      value: quantification_level,
      unit: DROUGHT_METRICS_UNIT_MAP.tier().unit,
      formattedValue: DROUGHT_METRICS_FORMATTER_MAP.tier(quantification_level),
    },
  };
};
