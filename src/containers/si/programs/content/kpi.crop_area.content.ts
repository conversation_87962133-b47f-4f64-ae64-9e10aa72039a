import {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {getUnit} from 'containers/si/utils/convert';

export const CROP_AREA_CONTENT = {
  name: (userUnitsSystem: MeasurementEnum) =>
    `Cropland ${getUnit(userUnitsSystem).unitName.plural}`,
  explainer: (panelLabel: string) => (userUnitsSystem: MeasurementEnum) => [
    `The total amount of cropland ${
      getUnit(userUnitsSystem).unitName.plural
    } growing commodities broken down ${panelLabel}. A cropland ${
      getUnit(userUnitsSystem).unitName.singular
    } in any given year is defined as ${
      userUnitsSystem === MeasurementEnum.MetricUnits ? 'a' : 'an'
    } ${
      getUnit(userUnitsSystem).unitName.singular
    } where a commodity is harvested. If commodities are harvested more than once within a year on any field, the ${
      getUnit(userUnitsSystem).unitName.plural
    } for that field are counted multiple times, once for each harvest.`,
  ],
  categoryColor: '8' as const,
};
