{"name": "flurosense-ui", "version": "0.1.0", "private": true, "scripts": {"start-js": "node scripts/start.js", "start": "export FLURO_ENV=local && ./create-config.sh && node scripts/start.js", "start:log": "export REDUX_LOGGER=true && pnpm start", "start:test": "export FLURO_ENV=test && ./create-config.sh && node scripts/start.js", "serve": "serve -s build", "build-js": "node scripts/build.js", "build-dev": "export FLURO_ENV=dev && ./create-config.sh && node --max_old_space_size=3584 scripts/build.js && cp robots/$(echo $FLURO_ENV).txt build/robots.txt", "build-dev2": "export FLURO_ENV=dev2 && ./create-config.sh && node --max_old_space_size=3584 scripts/build.js && cp robots/$(echo $FLURO_ENV).txt build/robots.txt", "build-dev3": "export FLURO_ENV=dev3 && ./create-config.sh && node --max_old_space_size=3584 scripts/build.js && cp robots/$(echo $FLURO_ENV).txt build/robots.txt", "build-prod-new": "export FLURO_ENV=prod-new && ./create-config.sh && node --max_old_space_size=3584 scripts/build.js && cp robots/prod.txt build/robots.txt", "build": "export FLURO_ENV=staging && ./create-config.sh && CI=false node --max_old_space_size=3584 scripts/build.js && cp robots/$(echo $FLURO_ENV).txt build/robots.txt", "build-prod": "export FLURO_ENV=prod && ./create-config.sh && node --max_old_space_size=3584 scripts/build.js && cp robots/$(echo $FLURO_ENV).txt build/robots.txt", "build-test": "export FLURO_ENV=test && ./create-config.sh && node --max_old_space_size=3584 scripts/build.js", "jest": "jest", "jest:silent": "jest --silent", "jest:watch": "jest --watch --maxWorkers=25%", "jest:ci": "jest --silent --maxWorkers=50%", "jest:coverage": "jest --maxWorkers=50% --silent --coverage", "jest:si-coverage": "pnpm jest:coverage --config ./jest-si.config.js", "jest:mrv-coverage": "pnpm jest:coverage --config ./jest-mrv.config.js", "start-server": "pnpm dlx serve@13.0.4 -l 2500 -s build", "type-coverage": "./node_modules/type-coverage/bin/type-coverage --strict", "prepare": "husky install", "artifactregistry-login": "pnpm dlx google-artifactregistry-auth", "pnpm:devPreinstall": "npx only-allow pnpm && pnpm artifactregistry-login", "lint": "pnpm run prettier:check && pnpm run eslint && pnpm run tsc", "lint:fix": "pnpm run prettier:write && pnpm run eslint:fix && pnpm run tsc", "eslint": " eslint 'src/**/*.{js,ts,tsx}'", "eslint:fix": " eslint 'src/**/*.{js,ts,tsx}' --fix --quiet", "eslint:quiet": "eslint 'src/**/*.{js,ts,tsx}' --quiet", "eslint:e2e": "pnpm run -C test_suite eslint '**/*.{js,ts,tsx}' ", "eslint:e2e-fix": "pnpm run -C test_suite eslint '**/*.{js,ts,tsx}' --fix", "tsc": "tsc --noEmit", "prettier:write": "prettier -w \"src/**/*.{js,jsx,ts,tsx,css,md,json}\" --config ./prettier.config.js --ignore-unknown", "prettier:write-e2e": "prettier -w \"test_suite/**/*.{js,jsx,ts,tsx,css,md,json}\" --config ./test_suite/prettier.config.js --ignore-unknown", "prettier:check": "prettier --list-different \"src/**/*.{js,jsx,ts,tsx,css,md,json}\" --config ./prettier.config.js --ignore-unknown", "prettier:check-e2e": "prettier --check \"test_suite/**/*.{js,jsx,ts,tsx,css,md,json}\" --config ./test_suite/prettier.config.js --ignore-unknown", "generate-si": "./scripts/generate-si-api.sh", "extract-new-translations": "node i18n/extract-new-translations.mjs", "upload-new-translations": "node i18n/upload-new-translations.mjs", "extract-and-upload-translations": "node i18n/extract-and-upload-translations.mjs", "generate-translations-terms-list": "formatjs extract './src/**/*.{js,ts,tsx,jsx}' --out-file i18n/extracted-translation-terms.json --ignore='./src/**/*.d.ts' --additional-function-names=t --id-interpolation-pattern '___[sha512:contenthash:base64:6]' --extract-source-location", "depcruise:report": "depcruise src", "depcruise:viz": "depcruise --include-only \"^src\" --collapse 4 --exclude \"(node_modules|__tests__|.stories.)\" --output-type dot src | dot -T svg > dependencygraph.svg", "ts-codegen": "pnpm run ts-codegen:swagger:mrv && pnpm run ts-codegen:graphql", "ts-codegen:swagger:mrv": "npx swagger-typescript-api --no-client -p http://mrv-service.int.prod.regrow.cloud/openapi.json -o src/__generated__/mrv -n mrvApi.types.ts", "ts-codegen:graphql": "graphql-codegen --config ./scripts/codegen.ts", "dpdm": "dpdm"}, "dependencies": {"@apollo/client": "^3.12.6", "@babel/core": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.17.12", "@babel/plugin-proposal-optional-chaining": "^7.14.5", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-proposal-private-property-in-object": "^7.20.5", "@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.16.3", "@cubejs-client/core": "0.35.23", "@cubejs-client/react": "0.35.48", "@formatjs/cli": "^6.1.0", "@geoman-io/leaflet-geoman-free": "^2.13.1", "@graphql-codegen/cli": "^5.0.0", "@graphql-codegen/client-preset": "^4.2.5", "@graphql-typed-document-node/core": "^3.2.0", "@hookform/resolvers": "^2.9.11", "@hot-loader/react-dom": "^17.0.1", "@ianvs/prettier-plugin-sort-imports": "^4.1.0", "@mapbox/geojsonhint": "^3.2.0", "@mapbox/mapbox-gl-draw": "^1.3.0", "@mapbox/togeojson": "^0.16.2", "@maphubs/tokml": "^0.6.1", "@mui/material": "^5.11.14", "@mui/styled-engine-sc": "^5.11.11", "@mui/system": "^5.13.6", "@mui/x-date-pickers-pro": "6.20.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11", "@popperjs/core": "2.9.3", "@react-leaflet/core": "^1.1.1", "@reduxjs/toolkit": "^1.7.0", "@regrow-internal/design-system": "1.1.144", "@sentry/react": "^8.9.2", "@sentry/webpack-plugin": "^2.18.0", "@svgr/webpack": "^8.1.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.2", "@turf/turf": "^6.5.0", "@types/convert-units": "^2.3.8", "@types/d3-array": "^3.2.1", "@types/dompurify": "^2.4.0", "@types/geojson": "^7946.0.10", "@types/history": "4.7.11", "@types/jest": "^29.4.0", "@types/leaflet": "^1.9.3", "@types/lodash": "^4.14.191", "@types/mapbox-gl": "^2.7.10", "@types/mapbox__mapbox-gl-draw": "^1.4.0", "@types/mixpanel-browser": "^2.49.0", "@types/node": "20.14.0", "@types/react": "^17.0.18", "@types/react-color": "^3.0.5", "@types/react-datepicker": "^4.19.5", "@types/react-dom": "^17.0.9", "@types/react-redux": "^7.1.20", "@types/react-router-dom": "^5.3.3", "@types/react-virtualized": "^9.21.13", "@types/react-window": "^1.8.8", "@types/redux-logger": "^3.0.12", "@types/shpjs": "^3.4.1", "@types/styled-components": "^5.1.12", "@types/supercluster": "^5.0.3", "@types/testing-library__jest-dom": "^5.14.6", "@types/uuid": "^8.3.1", "@types/zxcvbn": "^4.4.1", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "async-mutex": "^0.5.0", "axios": "^1.6.8", "babel-jest": "28.1.3", "babel-loader": "^8.2.5", "babel-plugin-styled-components": "^1.13.2", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.21.10", "buffer": "^6.0.3", "camelcase": "^8.0.0", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chart.js": "4.3.0", "classnames": "^2.3.1", "connected-react-router": "^6.9.2", "convert-units": "^2.3.4", "core-js": "^3.16.1", "country-state-picker": "^1.1.5", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "d3-array": "^3.2.4", "date-fns": "^2.29.1", "deepmerge": "^4.3.1", "dependency-cruiser": "^13.0.3", "dnd-core": "^16.0.1", "dompurify": "^2.4.3", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "dpdm": "^3.14.0", "esbuild": "^0.8.57", "esbuild-plugin-sass": "^0.3.4", "eslint": "^8.36.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-absolute-imports-only": "^1.0.1", "eslint-plugin-compat": "4.1.4", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-styled-components-a11y": "^0.1.0", "fast-deep-equal": "^3.1.3", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "graphql": "^16.8.1", "history": "^4.10.1", "html-webpack-plugin": "^5.5.0", "html2pdf.js": "^0.10.3", "husky": "^8.0.1", "immutability-helper": "^3.1.1", "inflection": "^3.0.0", "intl-messageformat": "^9.9.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jszip": "^3.10.1", "leaflet": "~1.7.1", "leaflet-draw": "^1.0.4", "leaflet-draw-locales": "^1.2.3", "leaflet-geosearch": "^3.4.0", "leaflet-measure": "^3.1.0", "leaflet.gridlayer.googlemutant": "^0.15.0", "lint-staged": "^10.5.4", "lodash": "^4.17.21", "mapbox-gl": "^2.10.0", "material-design-icons-iconfont": "^6.7.0", "mini-css-extract-plugin": "^2.4.5", "mixpanel-browser": "^2.50.0", "moment": "^2.29.4", "node-fetch": "^2.6.7", "normalize.css": "^8.0.1", "normalizr": "^3.6.1", "path-to-regexp": "^6.3.0", "polylabel": "^1.1.0", "postcss": "^8.4.28", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.8.3", "postcss-safe-parser": "^4.0.2", "posthog-js": "^1.143.0", "prettier": "^3.0.0", "proj4": "^2.8.0", "prop-types": "^15.7.2", "react": "^17.0.1", "react-chartjs-2": "5.2.0", "react-color": "^2.19.3", "react-datepicker": "^4.25.0", "react-dev-utils": "^12.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^17.0.2", "react-hook-form": "^7.51.3", "react-intl": "^6.5.2", "react-intl-tel-input": "^8.0.5", "react-leaflet": "^3.2.1", "react-leaflet-draw": "^0.19.8", "react-load-script": "0.0.6", "react-map-gl": "^7.0.19", "react-md": "^1.18.1", "react-popper": "2.2.5", "react-quill": "^2.0.0", "react-redux": "^7.2.6", "react-refresh": "^0.11.0", "react-responsive": "9.0.0-beta.4", "react-responsive-carousel": "^3.2.21", "react-router-dom": "^5.3.4", "react-select": "5.6.1", "react-toastify": "^8.2.0", "react-use-intercom": "2.0.0", "react-virtualized": "^9.22.3", "react-waypoint": "^9.0.3", "react-window": "^1.8.10", "reduce-reducers": "^1.0.4", "redux": "^4.1.2", "redux-beacon": "^2.1.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.1", "reproject": "^1.2.7", "reselect": "^4.1.5", "resolve": "^1.20.0", "sass-loader": "^10.4.1", "semver": "^7.5.2", "serve": "^14.2.1", "shpjs": "^4.0.4", "simple-statistics": "^7.8.8", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "styled-components": "^5.3.0", "supercluster": "^7.1.3", "swagger-typescript-api": "^12.0.3", "swr": "^2.2.5", "terser-webpack-plugin": "^5.2.5", "ts-jest": "^29.1.1", "ts-plugin-sort-import-suggestions": "^1.0.4", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.7.0", "uuid": "^8.3.2", "webpack": "^5.64.4", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1", "worker-loader": "^3.0.8", "yup": "^1.3.2", "zxcvbn": "^4.4.2"}, "pnpm": {"overrides": {"@mui/styled-engine": "npm:@mui/styled-engine-sc@6.0.0-alpha.1", "@mui/styled-engine-sc": "6.0.0-alpha.1", "csstype": "3.0.8", "decode-uri-component": "^0.2.1", "leaflet": "~1.7.1", "loader-utils": "^2.0.4", "nth-check": "^2.0.1", "react-error-overlay": "6.0.9", "d3-color": "3.1.0", "underscore": "^1.12.1", "ua-parser-js": "0.7.33", "node-fetch": "2.6.7", "minimatch@3.0.4": "3.0.5", "@babel/traverse": "^7.23.4"}}, "engines": {"node": "20.15.1", "pnpm": "8.7.4"}, "browserslist": [">0.5%", "not dead", "not ie <= 11", "not op_mini all", "not kaios >= 2.5", "iOS 14"], "packageManager": "pnpm@8.7.4+sha256.7d14339f572583eb550b7629f085d0443166ddbfa25fa32f8420179123fab98a"}