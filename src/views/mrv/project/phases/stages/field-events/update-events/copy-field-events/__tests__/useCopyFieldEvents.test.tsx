import type {MockedProviderProps} from '@apollo/client/testing';
import {MockedProvider} from '@apollo/client/testing';
import {act, waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import type {ReactNode} from 'react';
import React from 'react';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';
import {showNotification} from '_common/components/NotificationSnackbar';
import {SimpleProviders} from '_common/test_utils/renderWithProviders';

import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK as PHASE_MOCK} from 'views/mrv/project/phases/stages/__tests__/mock-data/graphqlMocks';
import {mockFieldId} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {useCopyFieldEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';
import {refetchFieldEvents} from 'views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents';
import {useNotificationContext} from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

import {
  BULK_COPY_FIELD_EVENTS_ERROR_MOCK,
  BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
  mockSelectedFieldEvents,
} from './mockData';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('store/useRedux');
jest.mock('views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents');
jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));

jest.mock('_common/components/NotificationSnackbar', () => ({
  showNotification: jest.fn(),
}));

jest.mock('views/mrv/project/phases/stages/notifications/NotificationContext', () => ({
  ...jest.requireActual('views/mrv/project/phases/stages/notifications/NotificationContext'),
  useNotificationContext: jest.fn(),
}));

const renderTestComponent = (mocks: MockedProviderProps['mocks'], children: ReactNode) => {
  return (
    <SimpleProviders>
      <MockedProvider mocks={mocks}>{children}</MockedProvider>
    </SimpleProviders>
  );
};

const renderHookWithProviders = (mocks: Array<any> = []) => {
  return renderHook(
    () =>
      useCopyFieldEvents({
        selectedFieldIds: [mockFieldId],
      }),
    {
      wrapper: ({children}) => renderTestComponent(mocks, children),
    }
  );
};

export const GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK = {
  ...PHASE_MOCK,
  request: {
    ...PHASE_MOCK.request,
    variables: {
      projectId: '1',
      fieldId: 'mock-field-id',
      phaseType: 'ENROLMENT',
      prefill_monitoring_phase: false,
    },
  },
};

describe('useCopyFieldEvents', () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
    typeof useSelectedFieldContext
  >;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const mockRefetchFieldEvents = refetchFieldEvents as jest.MockedFunction<
    typeof refetchFieldEvents
  >;

  const mockUseNotficationContext = useNotificationContext as jest.MockedFunction<
    typeof useNotificationContext
  >;
  const mockSetNotificationForFields = jest.fn();
  const mockSetPhaseNotification = jest.fn();
  const mockClearNotificationForField = jest.fn();

  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      selectedFieldEvents: mockSelectedFieldEvents,
    });

    mockUseNotficationContext.mockReturnValue({
      fieldNotifications: {},
      setNotificationForField: jest.fn(),
      setNotificationForFields: mockSetNotificationForFields,
      clearNotificationForField: mockClearNotificationForField,
      phaseNotification: {},
      setPhaseNotification: mockSetPhaseNotification,
      clearPhaseNotification: jest.fn(),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return copyFieldEvents function and loading state', () => {
    const {result} = renderHookWithProviders();

    expect(result.current.copyFieldEvents).toBeInstanceOf(Function);
    expect(result.current.copyFieldEventsLoading).toBe(false);
  });

  it('should successfully copy field events', async () => {
    const hookResult = renderHookWithProviders([
      BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
    ]);

    let result;
    await act(async () => {
      result = await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    expect(result).not.toBeNull();
    expect(result).toEqual(BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK.result!.data);
  });

  it('should handle copy field events error', async () => {
    const hookResult = renderHookWithProviders([
      BULK_COPY_FIELD_EVENTS_ERROR_MOCK,
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
    ]);

    try {
      await act(async () => {
        await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Overwrite);
      });
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }

    await waitFor(() => {
      expect(showNotification).toHaveBeenCalledWith({
        message: 'There was an issue copying your field events',
        type: 'error',
      });
    });
  });

  it('should not copy events when selectedFieldEvents.events is null', async () => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      selectedFieldEvents: {
        field: {id: '108264', name: 'Test Field'},
        events: null,
      },
    });

    const hookResult = renderHookWithProviders([
      BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
    ]);

    let result;
    await act(async () => {
      result = await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    expect(result).toBeNull();
  });

  it('should refetch field events on success', async () => {
    const hookResult = renderHookWithProviders([
      BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
    ]);

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    await waitFor(() => {
      expect(mockRefetchFieldEvents).toHaveBeenCalled();
    });
  });

  it('should set success notification for fields and phase when copy is successful', async () => {
    const hookResult = renderHookWithProviders([
      BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
    ]);

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    const expectedNotification = {
      message: '1 field has been updated.',
      title: '3 events successfully copied.',
    };

    await waitFor(() => {
      expect(mockSetNotificationForFields).toHaveBeenCalledWith(
        [mockFieldId],
        NotificationLevel.SUCCESS,
        expectedNotification
      );
      expect(mockSetPhaseNotification).toHaveBeenCalledWith(
        NotificationLevel.SUCCESS,
        expectedNotification
      );
    });
  });

  it('should not set notification when copy fails with errors and no data', async () => {
    const hookResult = renderHookWithProviders([
      BULK_COPY_FIELD_EVENTS_ERROR_MOCK,
      GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
    ]);

    try {
      await act(async () => {
        await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Overwrite);
      });
    } catch (error) {
      // Expected to throw
    }

    expect(mockSetNotificationForFields).not.toHaveBeenCalled();
    expect(mockSetPhaseNotification).not.toHaveBeenCalled();
  });
});
