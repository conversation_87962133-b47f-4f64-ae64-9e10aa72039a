import {capitalizeFirstLetter} from '_common/utils/string';

import {HEATWAVE_OCCURRENCES_CONTENT} from 'containers/si/programs/content/kpi.heat_stress.content';
import {
  HEAT_STRESS_METRICS_FORMATTER_MAP,
  HEAT_STRESS_METRICS_UNIT_MAP,
  makeHeatStressMetrics,
  type HeatStressMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.heat_stress.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const CARD_CONTENT = {
  title: capitalizeFirstLetter(HEATWAVE_OCCURRENCES_CONTENT.name),
  isDownwardChangePositive: true,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: HEATWAVE_OCCURRENCES_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: HEATWAVE_OCCURRENCES_CONTENT.explainer(PANEL_LABEL.BY_AREA),
    sortDirection: 'DESC',
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: HEATWAVE_OCCURRENCES_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
    sortDirection: 'DESC',
  },
} as const;

export const KPI = 'heat_stress';
export const FORMATTER_MAP = HEAT_STRESS_METRICS_FORMATTER_MAP;
export const UNIT_MAP = HEAT_STRESS_METRICS_UNIT_MAP;

export const METRIC_MAP: Pick<
  MetricLookupMap<HeatStressMetrics>,
  'primaryMetricKey' | 'tierMetricKey'
> = {
  primaryMetricKey: 'heatStressOccurrences',
  tierMetricKey: 'tier',
};

export const CATEGORY_COLOR_KEY = HEATWAVE_OCCURRENCES_CONTENT.categoryColor;

export const TRANSFORMER = makeHeatStressMetrics;
