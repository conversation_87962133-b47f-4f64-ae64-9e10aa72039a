import {VOLUME_CAPPED_NET_CONTENT} from 'containers/si/programs/content/kpi.volume_capped_net_kg.content';
import {
  makeVolumeCappedNetMetrics,
  VOLUME_CAPPED_NET_METRICS_FORMATTER_MAP,
  VOLUME_CAPPED_NET_METRICS_UNIT_MAP,
  type VolumeCappedNetMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_capped_net_kg.transformation';
import {
  NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const KPI = 'volume_weighted_net_kg';
export const FORMATTER_MAP = VOLUME_CAPPED_NET_METRICS_FORMATTER_MAP;
export const UNIT_MAP = VOLUME_CAPPED_NET_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<VolumeCappedNetMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'volumeCappedNetMass',
};
export const TRANSFORMER = makeVolumeCappedNetMetrics;
export const CATEGORY_COLOR_KEY = VOLUME_CAPPED_NET_CONTENT.categoryColor;

export const CARD_CONTENT = {isDownwardChangePositive: true};

export const PANEL_CONTENT = {
  ByYear: {
    title: VOLUME_CAPPED_NET_CONTENT.name(PANEL_LABEL.OVER_TIME),
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: VOLUME_CAPPED_NET_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: VOLUME_CAPPED_NET_CONTENT.name(PANEL_LABEL.BY_CROP_TYPE),
    label: PANEL_LABEL.BY_CROP_TYPE,
    isDisabledMessage: (hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
    tooltipContent: VOLUME_CAPPED_NET_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: VOLUME_CAPPED_NET_CONTENT.name(PANEL_LABEL.BY_AREA),
    label: PANEL_LABEL.BY_AREA,
    isDisabledMessage: (hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
    tooltipContent: VOLUME_CAPPED_NET_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
