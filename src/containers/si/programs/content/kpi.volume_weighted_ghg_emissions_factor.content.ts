import {isDefined} from '_common/utils/typeGuards';

import {CH4, N2O} from 'containers/si/constants';

export const VOLUME_WEIGHTED_GHG_EF_CONTENT = {
  name: (panelLabel?: string) =>
    `Field GHG emission factor ${isDefined(panelLabel) ? `${panelLabel} ` : ''}(volume-weighted)`,
  explainer: (panelLabel: string) => [
    `The average amount of field-based ${N2O} and ${CH4} emissions produced per kg yield ${panelLabel}, weighted by the volume purchased from each subregion. This is calculated by dividing the sum of the total field-based ${N2O} and ${CH4} emissions produced by the volume purchased in each subregion by the total volume purchased across all subregions.`,
    `Note that this datapoint can only be provided per commodity, is only useful when weighting volumes across multiple subregions, and is omitted for commodities/subregions/years without volumes.`,
  ],
  categoryColor: '2' as const,
};
