import {type MeasurementEnum} from '_common/utils/measurement-unit-options';

import {CROP_AREA_CONTENT} from 'containers/si/programs/content/kpi.crop_area.content';
import {
  CROPAREAMETRICS_FORMATTER_MAP,
  CROPAREAMETRICS_UNIT_MAP,
  makeCropAreaMetrics,
  type CropAreaMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.crop_area.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const KPI = 'crop_area';
export const FORMATTER_MAP = CROPAREAMETRICS_FORMATTER_MAP;
export const UNIT_MAP = CROPAREAMETRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<CropAreaMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'crop_area',
};
export const TRANSFORMER = makeCropAreaMetrics;
export const CATEGORY_COLOR_KEY = CROP_AREA_CONTENT.categoryColor;

const CARD_CONTENT = {
  title: (userUnitsSystem: MeasurementEnum) => CROP_AREA_CONTENT.name(userUnitsSystem),
};

export const PANEL_CONTENT = {
  ByYear: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: CROP_AREA_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: CROP_AREA_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: CROP_AREA_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
