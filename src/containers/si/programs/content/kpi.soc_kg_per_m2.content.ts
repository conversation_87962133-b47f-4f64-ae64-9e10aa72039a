import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {getUnit} from 'containers/si/utils/convert';

export const SOC_PER_AREA_CONTENT = {
  name: (userUnitsSystem: MeasurementEnum) =>
    `Field SOC sequestration per ${getUnit(userUnitsSystem).unitName.singular}`,
  explainer:
    (panelLabel: string, denominatorString: string) => (userUnitsSystem: MeasurementEnum) => [
      `The average amount of SOC sequestered per ${
        getUnit(userUnitsSystem).unitName.singular
      } broken down ${panelLabel}. The denominator is the total cropland ${
        getUnit(userUnitsSystem).unitName.plural
      } of the ${denominatorString}.`,
      `Note that standard error can only be calculated by commodity and will only be provided when a single commodity is selected.`,
    ],
  categoryColor: '2' as const,
};
