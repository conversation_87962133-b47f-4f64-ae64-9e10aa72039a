import {GHG_EF_CONTENT} from 'containers/si/programs/content/kpi.ghg_emissions_factor.content';
import {
  GHG_EF_METRICS_FORMATTER_MAP,
  GHG_EF_METRICS_UNIT_MAP,
  makeGhgEFMetrics,
  type GhgEFMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_emissions_factor.transformation';
import {
  NO_DATA_FOR_MULTIPLE_CROPS_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

export const KPI = 'ghg_emissions_factor';
export const FORMATTER_MAP = GHG_EF_METRICS_FORMATTER_MAP;
export const UNIT_MAP = GHG_EF_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<
  MetricLookupMap<GhgEFMetrics>,
  'primaryMetricKey' | 'uncertaintyMetricKey'
> = {
  primaryMetricKey: 'ghgEmissionsPerYield',
  uncertaintyMetricKey: 'ghgEFStdErr',
};
export const TRANSFORMER = makeGhgEFMetrics;
export const CATEGORY_COLOR_KEY = GHG_EF_CONTENT.categoryColor;

const CARD_CONTENT = {
  title: GHG_EF_CONTENT.name,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
    isDownwardChangePositive: true,
    tooltipContent: GHG_EF_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    isDisabledMessage: (_filters: KPIDataFiltersState) => undefined,
    fetchSummary: 'crop_type',
    tooltipContent: GHG_EF_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'boundary',
    tooltipContent: GHG_EF_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
