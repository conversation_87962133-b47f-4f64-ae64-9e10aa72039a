import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {SOC_PER_AREA_CONTENT} from 'containers/si/programs/content/kpi.soc_kg_per_m2.content';
import {
  makeSocMetrics,
  SOCMETRICS_FORMATTER_MAP,
  SOCMETRICS_UNIT_MAP,
  type SocMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.soc_kg_per_m2.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const KPI = 'soc_kg_per_m2';
export const FORMATTER_MAP = SOCMETRICS_FORMATTER_MAP;
export const UNIT_MAP = SOCMETRICS_UNIT_MAP;
export const METRIC_MAP: Pick<
  MetricLookupMap<SocMetrics>,
  'primaryMetricKey' | 'uncertaintyMetricKey'
> = {
  primaryMetricKey: 'dSocMassPerArea',
  uncertaintyMetricKey: 'dSocStdErr',
};
export const TRANSFORMER = makeSocMetrics;
export const CATEGORY_COLOR_KEY = SOC_PER_AREA_CONTENT.categoryColor;

const CARD_CONTENT = {
  title: SOC_PER_AREA_CONTENT.name,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: SOC_PER_AREA_CONTENT.explainer(PANEL_LABEL.OVER_TIME, 'specified subregion(s)'),
  },
  ByCrop: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: SOC_PER_AREA_CONTENT.explainer(
      PANEL_LABEL.BY_CROP_TYPE,
      'specified subregion(s) growing the given commodity'
    ),
  },
  ByBoundary: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: SOC_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_AREA, 'given subregion'),
  },
} as const;
