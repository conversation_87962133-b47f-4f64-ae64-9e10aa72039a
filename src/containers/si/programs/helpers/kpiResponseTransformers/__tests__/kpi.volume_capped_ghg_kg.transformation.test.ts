import {KPIVolumeCappedGhgKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIVolumeCappedGhgMetricsMock';
import {makeVolumeCappedGhgMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_capped_ghg_kg.transformation';

describe('makeVolumeWeightedGhgMetrics', () => {
  it('Should return VolumeWeightedGhgMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makeVolumeCappedGhgMetrics(KPIVolumeCappedGhgKgSummarizeByCropTypeMock.crop_type_summary[1]!)
    ).toEqual({volumeCappedGhgMass: {formattedValue: '0.01', unit: 'mt', value: 0.01}});
    expect(
      makeVolumeCappedGhgMetrics(KPIVolumeCappedGhgKgSummarizeByCropTypeMock.crop_type_summary[2]!)
    ).toEqual({volumeCappedGhgMass: {formattedValue: '0.02', unit: 'mt', value: 0.02}});
    expect(
      makeVolumeCappedGhgMetrics(KPIVolumeCappedGhgKgSummarizeByCropTypeMock.crop_type_summary[3]!)
    ).toEqual({volumeCappedGhgMass: {formattedValue: '0.03', unit: 'mt', value: 0.03}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeCappedGhgMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_ghg_kg: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeCappedGhgMetrics({
        volume_weighted_ghg_kg: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeCappedGhgMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeCappedGhgMetrics({
        volume_weighted_ghg_kg: Infinity,
      })
    ).toEqual(null);
  });
});
