import {VOLUME_WEIGHTED_SOC_EF_CONTENT} from 'containers/si/programs/content/kpi.volume_weighted_soc_emissions_factor.content';
import {
  makeVolumeWeightedSocEFMetrics,
  VOLUMEWEIGHTEDSOCEFMETRICS_FORMATTER_MAP,
  VOLUMEWEIGHTEDSOCEFMETRICS_UNIT_MAP,
  type VolumeWeightedSocEFMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_weighted_soc_emissions_factor.transformation';
import {
  NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY,
  NO_DATA_FOR_MULTIPLE_CROPS_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

export const KPI = 'volume_weighted_soc_emissions_factor';
export const FORMATTER_MAP = VOLUMEWEIGHTEDSOCEFMETRICS_FORMATTER_MAP;
export const UNIT_MAP = VOLUMEWEIGHTEDSOCEFMETRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<VolumeWeightedSocEFMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'volumeWeighteSocEmissionsPerYeild',
};
export const TRANSFORMER = makeVolumeWeightedSocEFMetrics;
export const CATEGORY_COLOR_KEY = VOLUME_WEIGHTED_SOC_EF_CONTENT.categoryColor;

export const CARD_CONTENT = {isDownwardChangePositive: false};

export const PANEL_CONTENT = {
  ByYear: {
    title: VOLUME_WEIGHTED_SOC_EF_CONTENT.name(PANEL_LABEL.OVER_TIME),
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: VOLUME_WEIGHTED_SOC_EF_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
    isDisabledMessage: (filters: KPIDataFiltersState, _hasPurchaseVolume: boolean) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
  },
  ByCrop: {
    title: VOLUME_WEIGHTED_SOC_EF_CONTENT.name(PANEL_LABEL.BY_CROP_TYPE),
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: VOLUME_WEIGHTED_SOC_EF_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
    isDisabledMessage: (_filters: KPIDataFiltersState, hasPurchaseVolume: boolean) =>
      !hasPurchaseVolume ? NO_DATA_FOR_MISSING_PURCHASE_VOLUME_COPY : undefined,
    fetchSummary: 'crop_type',
  },
} as const;
