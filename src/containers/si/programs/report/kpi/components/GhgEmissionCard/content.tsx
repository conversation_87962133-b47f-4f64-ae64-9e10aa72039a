import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {GHG_PER_AREA_CONTENT} from 'containers/si/programs/content/kpi.ghg_kg_per_m2.content';
import {
  GHGMETRICS_FORMATTER_MAP,
  GHGMETRICS_UNIT_MAP,
  makeGhgMetrics,
  type GhgMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.ghg_kg_per_m2.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';

export const KPI = 'ghg_kg_per_m2';
export const FORMATTER_MAP = GHGMETRICS_FORMATTER_MAP;
export const UNIT_MAP = GHGMETRICS_UNIT_MAP;
export const METRIC_MAP: Pick<
  MetricLookupMap<GhgMetrics>,
  'primaryMetricKey' | 'uncertaintyMetricKey'
> = {
  primaryMetricKey: 'ghgMassPerArea',
  uncertaintyMetricKey: 'ghgStdErr',
};
export const TRANSFORMER = makeGhgMetrics;
export const CATEGORY_COLOR_KEY = GHG_PER_AREA_CONTENT.categoryColor;

export const CARD_CONTENT = {
  title: GHG_PER_AREA_CONTENT.name,
  isDownwardChangePositive: true,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: GHG_PER_AREA_CONTENT.explainer(PANEL_LABEL.OVER_TIME, 'specified subregion(s)'),
  },
  ByCrop: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: GHG_PER_AREA_CONTENT.explainer(
      PANEL_LABEL.BY_CROP_TYPE,
      'specified subregion(s) growing the given commodity'
    ),
  },
  ByBoundary: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: GHG_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_AREA, 'given subregion'),
  },
} as const;
