import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {
  CATEGORY_COLOR_KEY,
  FORMATTER_MAP,
  KPI,
  METRIC_MAP,
  TRANSFORMER,
  UNIT_MAP,
} from 'containers/si/programs/report/kpi/components/YoYTemperatureChangeCard/content';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import type {SummaryTypeChartProps} from 'containers/si/programs/report/kpi/types';

export type CardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

export const useYoYTemperatureChangeCardData = (): CardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: ['annualized', 'boundary', 'crop_type'],
    commonFilters,
    kpiTransformer: TRANSFORMER,
  });

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const primaryMetricTileContentWithoutComparisonMetrics: PrimaryMetricTileProps | null =
    primaryMetricTileContent
      ? {...primaryMetricTileContent, comparisonMetric1: null, comparisonMetric2: null}
      : null;

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    primaryMetricTileContent: primaryMetricTileContentWithoutComparisonMetrics,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
