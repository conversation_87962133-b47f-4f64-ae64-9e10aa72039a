import {isDefined} from '_common/utils/typeGuards';

export const VOLUME_CAPPED_SOC_CONTENT = {
  name: (panelLabel?: string) =>
    `Total field SOC sequestration ${isDefined(panelLabel) ? `${panelLabel} ` : ''}(volume-capped)`,
  explainer: (panelLabel: string) => [
    `The sum of all SOC sequestered broken down ${panelLabel}, specific to the volume of the commodities purchased.`,
    `Note that standard error is not available for volume-based data and that volume-based data is omitted for commodities/subregions/years without volumes.`,
  ],
  categoryColor: '2' as const,
};
