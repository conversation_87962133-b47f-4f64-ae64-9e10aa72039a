import {act, waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import type {ReactNode} from 'react';
import React from 'react';

import {SimpleProviders} from '_common/test_utils/renderWithProviders';

import {
  NotificationContextProvider,
  useNotificationContext,
} from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');

const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
  typeof useSelectedFieldContext
>;

const renderTestComponent = (children: ReactNode) => {
  return (
    <SimpleProviders>
      <NotificationContextProvider>{children}</NotificationContextProvider>
    </SimpleProviders>
  );
};

const renderHookWithProvider = () => {
  return renderHook(() => useNotificationContext(), {
    wrapper: ({children}) => renderTestComponent(children),
  });
};
const initialState = {
  selectedField: null,
  selectedFarm: null,
  farms: [],
  setSelectedField: jest.fn(),
  selectedFieldEvents: {field: null, events: null},
  setSelectedFieldEvents: jest.fn(),
  clearSelectedFieldEvents: jest.fn(),
  isLoading: false,
  error: null,
};

describe('FieldNotificationContext', () => {
  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue(initialState);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should provide initial empty state', () => {
    const {result} = renderHookWithProvider();

    expect(result.current.fieldNotifications).toEqual({});
    expect(result.current.setNotificationForField).toBeInstanceOf(Function);
    expect(result.current.setNotificationForFields).toBeInstanceOf(Function);
    expect(result.current.clearNotificationForField).toBeInstanceOf(Function);
  });

  it('should set notification for a single field', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        NotificationLevel.SUCCESS,
        'Test success message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [NotificationLevel.SUCCESS]: ['Test success message'],
      },
    });
  });

  it('should set notifications for multiple fields', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForFields(
        ['field-1', 'field-2'],
        NotificationLevel.SUCCESS,
        'Bulk success message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [NotificationLevel.SUCCESS]: ['Bulk success message'],
      },
      'field-2': {
        [NotificationLevel.SUCCESS]: ['Bulk success message'],
      },
    });
  });

  it('should append multiple notifications for the same field and level', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.SUCCESS, 'First message');
    });

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        NotificationLevel.SUCCESS,
        'Second message'
      );
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [NotificationLevel.SUCCESS]: ['First message', 'Second message'],
      },
    });
  });

  it('should handle different notification levels for the same field', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.setNotificationForField(
        'field-1',
        NotificationLevel.SUCCESS,
        'Success message'
      );
    });

    act(() => {
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, 'Error message');
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [NotificationLevel.SUCCESS]: ['Success message'],
        [NotificationLevel.ERROR]: ['Error message'],
      },
    });
  });

  it('should clear specific notification level for a field', () => {
    const {result} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField(
        'field-1',
        NotificationLevel.SUCCESS,
        'Success message'
      );
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, 'Error message');
    });

    // Clear only success notifications
    act(() => {
      result.current.clearNotificationForField('field-1', NotificationLevel.SUCCESS);
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-1': {
        [NotificationLevel.SUCCESS]: undefined,
        [NotificationLevel.ERROR]: ['Error message'],
      },
    });
  });

  it('should clear all notifications for a field when no level specified', () => {
    const {result} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField(
        'field-1',
        NotificationLevel.SUCCESS,
        'Success message'
      );
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, 'Error message');
      result.current.setNotificationForField(
        'field-2',
        NotificationLevel.WARNING,
        'Warning message'
      );
    });

    // Clear all notifications for field-1
    act(() => {
      result.current.clearNotificationForField('field-1');
    });

    expect(result.current.fieldNotifications).toEqual({
      'field-2': {
        [NotificationLevel.WARNING]: ['Warning message'],
      },
    });
  });

  it('should automatically clear success notifications when field is selected', async () => {
    const {result, rerender} = renderHookWithProvider();

    // Set up notifications
    act(() => {
      result.current.setNotificationForField(
        'field-1',
        NotificationLevel.SUCCESS,
        'Success message'
      );
      result.current.setNotificationForField('field-1', NotificationLevel.ERROR, 'Error message');
    });

    // Simulate field selection
    mockUseSelectedFieldContext.mockReturnValue({
      selectedField: {id: 'field-1', name: 'Test Field'},
      selectedFarm: null,
      farms: [],
      setSelectedField: jest.fn(),
      selectedFieldEvents: {field: null, events: null},
      setSelectedFieldEvents: jest.fn(),
      clearSelectedFieldEvents: jest.fn(),
      isLoading: false,
      error: null,
    });

    rerender();

    await waitFor(() => {
      expect(result.current.fieldNotifications).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: undefined,
          [NotificationLevel.ERROR]: ['Error message'],
        },
      });
    });
  });

  it('should handle clearing notifications for non-existent field gracefully', () => {
    const {result} = renderHookWithProvider();

    act(() => {
      result.current.clearNotificationForField('non-existent-field');
    });

    expect(result.current.fieldNotifications).toEqual({});
  });

  describe('setNotificationForFields', () => {
    it('should set notifications for multiple fields at once', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setNotificationForFields(
          ['field-1', 'field-2', 'field-3'],
          NotificationLevel.SUCCESS,
          'Bulk success message'
        );
      });

      expect(result.current.fieldNotifications).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: ['Bulk success message'],
        },
        'field-2': {
          [NotificationLevel.SUCCESS]: ['Bulk success message'],
        },
        'field-3': {
          [NotificationLevel.SUCCESS]: ['Bulk success message'],
        },
      });
    });

    it('should append to existing notifications when setting for multiple fields', () => {
      const {result} = renderHookWithProvider();

      // Set initial notification for field-1
      act(() => {
        result.current.setNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          'Initial message'
        );
      });

      // Set bulk notifications including field-1
      act(() => {
        result.current.setNotificationForFields(
          ['field-1', 'field-2'],
          NotificationLevel.SUCCESS,
          'Bulk message'
        );
      });

      expect(result.current.fieldNotifications).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: ['Initial message', 'Bulk message'],
        },
        'field-2': {
          [NotificationLevel.SUCCESS]: ['Bulk message'],
        },
      });
    });

    it('should handle empty field array gracefully', () => {
      const {result} = renderHookWithProvider();

      act(() => {
        result.current.setNotificationForFields([], NotificationLevel.SUCCESS, 'No fields message');
      });

      expect(result.current.fieldNotifications).toEqual({});
    });

    it('should preserve existing notifications for different levels when setting bulk notifications', () => {
      const {result} = renderHookWithProvider();

      // Set initial error notification for field-1
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.ERROR, 'Error message');
      });

      // Set bulk success notifications
      act(() => {
        result.current.setNotificationForFields(
          ['field-1', 'field-2'],
          NotificationLevel.SUCCESS,
          'Success message'
        );
      });

      expect(result.current.fieldNotifications).toEqual({
        'field-1': {
          [NotificationLevel.ERROR]: ['Error message'],
          [NotificationLevel.SUCCESS]: ['Success message'],
        },
        'field-2': {
          [NotificationLevel.SUCCESS]: ['Success message'],
        },
      });
    });
  });

  describe('Auto-clear behavior', () => {
    it('should not clear success notifications if field has no success notifications', async () => {
      const {result, rerender} = renderHookWithProvider();

      // Set up error notification only
      act(() => {
        result.current.setNotificationForField('field-1', NotificationLevel.ERROR, 'Error message');
      });

      // Simulate field selection
      mockUseSelectedFieldContext.mockReturnValue({
        selectedField: {id: 'field-1', name: 'Test Field'},
        selectedFarm: null,
        farms: [],
        setSelectedField: jest.fn(),
        selectedFieldEvents: {field: null, events: null},
        setSelectedFieldEvents: jest.fn(),
        clearSelectedFieldEvents: jest.fn(),
        isLoading: false,
        error: null,
      });

      rerender();

      await waitFor(() => {
        expect(result.current.fieldNotifications).toEqual({
          'field-1': {
            [NotificationLevel.ERROR]: ['Error message'],
          },
        });
      });
    });

    it('should not clear notifications when no field is selected', async () => {
      const {result, rerender} = renderHookWithProvider();

      // Set up notifications
      act(() => {
        result.current.setNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          'Success message'
        );
      });

      // Simulate no field selection (selectedField remains null)
      rerender();

      await waitFor(() => {
        expect(result.current.fieldNotifications).toEqual({
          'field-1': {
            [NotificationLevel.SUCCESS]: ['Success message'],
          },
        });
      });
    });

    it('should only clear success notifications for the selected field, not other fields', async () => {
      const {result, rerender} = renderHookWithProvider();

      // Set up notifications for multiple fields
      act(() => {
        result.current.setNotificationForField(
          'field-1',
          NotificationLevel.SUCCESS,
          'Field 1 success'
        );
        result.current.setNotificationForField(
          'field-2',
          NotificationLevel.SUCCESS,
          'Field 2 success'
        );
      });

      // Simulate field-1 selection
      mockUseSelectedFieldContext.mockReturnValue({
        selectedField: {id: 'field-1', name: 'Test Field 1'},
        selectedFarm: null,
        farms: [],
        setSelectedField: jest.fn(),
        selectedFieldEvents: {field: null, events: null},
        setSelectedFieldEvents: jest.fn(),
        clearSelectedFieldEvents: jest.fn(),
        isLoading: false,
        error: null,
      });

      rerender();

      await waitFor(() => {
        expect(result.current.fieldNotifications).toEqual({
          'field-1': {
            [NotificationLevel.SUCCESS]: undefined,
          },
          'field-2': {
            [NotificationLevel.SUCCESS]: ['Field 2 success'],
          },
        });
      });
    });
  });
});
