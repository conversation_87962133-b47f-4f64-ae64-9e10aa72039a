import React, {useMemo} from 'react';

import {Typography, useTheme, type SvgIconProps} from '@regrow-internal/design-system';

import type {NotificationLookup} from 'views/mrv/project/phases/stages/notifications/types';
import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';

const notificationColourLookup = {
  [NotificationLevel.SUCCESS]: 'semanticPalette.highlight.success',
  [NotificationLevel.WARNING]: 'semanticPalette.highlight.warning',
  [NotificationLevel.ERROR]: 'semanticPalette.highlight.error',
};

export const FieldNotificationIndicator = ({
  notifications,
}: {
  notifications: NotificationLookup | null;
}) => {
  const theme = useTheme();
  const errorTypeToShow = useMemo(() => {
    if (!notifications) return null;
    if (notifications[NotificationLevel.ERROR]?.length) {
      return NotificationLevel.ERROR;
    }
    if (notifications[NotificationLevel.WARNING]?.length) {
      return NotificationLevel.WARNING;
    }
    if (notifications[NotificationLevel.SUCCESS]?.length) {
      return NotificationLevel.SUCCESS;
    }
    return null;
  }, [notifications]);

  if (!errorTypeToShow) return null;

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const colour = notificationColourLookup[errorTypeToShow] as SvgIconProps['color'];

  return (
    <Typography fontSize={theme.spacing(4.5)} color={colour} aria-label={errorTypeToShow}>
      &#9679;
    </Typography>
  );
};
