export const AVG_TEMP_CONTENT = {
  name: 'average temperature',
  explainer: (panelLabel: string) => [
    `The average annual temperature broken down ${panelLabel}.`,
    `Note: If field level data is available for the entire specified geography, average temperature is determined for the areas which grow the specified commodities. Otherwise, the average temperature is determined over the entire geography.`,
  ],
  categoryColor: '4' as const,
};
export const AVG_MAX_TEMP_CONTENT = {
  name: 'average max temperature',
  explainer: (panelLabel: string) => [
    `The annual average of the max daily temperature broken down ${panelLabel}.`,
    `Note: If field level data is available for the entire specified geography, the average max temperature is determined for the areas which grow the specified commodities. Otherwise, the average max temperature is determined over the entire geography.`,
  ],
  categoryColor: '4' as const,
};
export const YOY_TEMP_CHANGE_CONTENT = {
  name: 'YoY temperature change',
  explainer: (panelLabel: string) => [
    `The difference in average annual temperature from the previous year broken down ${panelLabel}.`,
    `Note: If field level data is available for the entire specified geography, the YoY temperature change is determined for the areas which grow the specified commodities. Otherwise, the YoY temperature change is determined over the entire geography.`,
  ],
  categoryColor: '4' as const,
};
