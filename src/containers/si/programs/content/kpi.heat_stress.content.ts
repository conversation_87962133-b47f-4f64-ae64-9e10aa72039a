export const HEAT_STRESS_CONTENT = {
  name: 'heat stress',
  explainer: (panelLabel: string) => [
    `The average number of days in a year under heat stress (over 40°C) broken down ${panelLabel}.`,
    `Note: If field level data is available for the entire specified geography, heat stress is determined for the areas which grow the specified commodities. Otherwise, heat stress is determined over the entire geography.`,
  ],
  categoryColor: '4' as const,
};
export const HEATWAVE_OCCURRENCES_CONTENT = {
  name: 'heatwave occurrences',
  explainer: (panelLabel: string) => [
    `The average annual occurrences of heatwaves broken down ${panelLabel} A heatwave is defined as two or more consecutive days with a heat index (apparent temperature) over 40°C.`,
    `Note: If field level data is available for the entire specified geography, heatwave occurrences are determined for the areas which grow the specified commodities. Otherwise, heatwave occurrences are determined over the entire geography.`,
  ],
  categoryColor: '4' as const,
};
