import type {PropsWithChildren} from 'react';
import React from 'react';

import {
  Box,
  IconButton,
  ResizableFlexContainer,
  Skeleton,
  SvgIcon,
  useTheme,
} from '@regrow-internal/design-system';

import {isNonEmptyArray, isNumber} from '_common/utils/typeGuards';

import {REGROW_STAGE_HELP_LINKS} from 'views/mrv/project/constants';
import {FarmFieldNavigation} from 'views/mrv/project/phases/stages/farm-field-navigation/FarmFieldNavigation';
import {PrevNextFieldButtons} from 'views/mrv/project/phases/stages/farm-field-navigation/PrevNextFieldButtons';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

import {FieldEventsTableHeader} from './FieldEventsTableHeader';
import {FieldEventsTableIndicator} from './FieldEventsTableIndicator';

export const FieldEventsTableWrapper = React.memo(({children}: PropsWithChildren<unknown>) => {
  const theme = useTheme();

  const {farms, selectedFarm, selectedField, isLoading, setSelectedField} =
    useSelectedFieldContext();
  const {currentStage, stageCompletion} = useStageContext();

  const helpLink =
    currentStage?.type && currentStage.type in REGROW_STAGE_HELP_LINKS
      ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        REGROW_STAGE_HELP_LINKS[currentStage.type as keyof typeof REGROW_STAGE_HELP_LINKS]
      : null;

  const onResizeEnd = (widths: Array<number>) => {
    localStorage.setItem('resizedFieldEventsTableWidths', JSON.stringify(widths));
  };

  const savedWidthsRaw = localStorage.getItem('resizedFieldEventsTableWidths');

  const savedWidths: Array<number> | null = React.useMemo(() => {
    try {
      if (savedWidthsRaw) {
        const parsed: unknown = JSON.parse(savedWidthsRaw);
        if (isNonEmptyArray(parsed) && parsed.every((item: unknown) => isNumber(item))) {
          return parsed;
        }
      }
      return null;
    } catch {
      return null;
    }
  }, [savedWidthsRaw]);

  return (
    <Box display="flex" gap={theme.spacing(3)} flexGrow={1} overflow="hidden">
      <ResizableFlexContainer
        display="flex"
        width="100%"
        height="100%"
        mt={5}
        onResizeEnd={onResizeEnd}
      >
        <Box width={savedWidths ? savedWidths[0] : 170} maxWidth="33%" minWidth={100} height="100%">
          <FarmFieldNavigation
            farms={farms}
            selectedFieldId={selectedField ? selectedField.id : null}
            onFieldClick={setSelectedField}
            fieldCompletionLookup={stageCompletion}
            isLoading={isLoading}
          />
        </Box>
        <Box
          width={savedWidths ? savedWidths[1] : '67%'}
          height="100%"
          border={1}
          px={3}
          pt={3}
          mr={4}
          borderRadius={theme.borderRadii.md}
          borderColor={theme.palette.grey[200]}
          maxWidth={'93%'}
          minWidth={'67%'}
          sx={{
            backgroundColor: theme.palette.background.paper,
            scrollbarWidth: 'thin',
          }}
        >
          {isLoading && !selectedFarm && !selectedField ? (
            <Box
              display="inline-flex"
              alignContent="center"
              alignItems="center"
              gap={2}
              aria-label="field events table loading"
              aria-busy={true}
              aria-live="polite"
            >
              <Skeleton variant="rounded" width={25} height={25} />
              <Skeleton variant="rounded" width={125} />
            </Box>
          ) : (
            <>
              <Box display="flex">
                <>
                  <FieldEventsTableHeader farm={selectedFarm} field={selectedField} />
                  <Box display="flex" alignItems="center" ml="auto">
                    <FieldEventsTableIndicator field={selectedField} />
                    <PrevNextFieldButtons
                      farms={farms}
                      selectedFieldId={selectedField?.id ?? null}
                      setSelectedField={setSelectedField}
                    />
                    {helpLink && (
                      <Box ml={1}>
                        <IconButton href={helpLink} target="_blank">
                          <SvgIcon type="question-circled" fontSize="h5" color="secondary" />
                        </IconButton>
                      </Box>
                    )}
                  </Box>
                </>
              </Box>

              {children}
            </>
          )}
        </Box>
      </ResizableFlexContainer>
    </Box>
  );
});
