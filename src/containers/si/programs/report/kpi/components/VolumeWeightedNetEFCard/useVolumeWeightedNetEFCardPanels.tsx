import React, {useMemo} from 'react';

import {CardTooltip} from 'containers/si/programs/report/kpi/components/CardTooltip';
import {OverTimePanel} from 'containers/si/programs/report/kpi/components/OverTimePanel';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/VolumeWeightedNetEFCard/content';
import {type UseVolumeWeightedNetEFCardData} from 'containers/si/programs/report/kpi/components/VolumeWeightedNetEFCard/useVolumeWeightedNetEFCardData';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useSortedPaginatedSummaryTypeBarChart} from 'containers/si/programs/report/kpi/hooks/useSortedPaginatedSummaryTypeBarChart';

export const useVolumeWeightedNetEFCardPanels = ({
  isLoading,
  data: {
    primaryMetricTileContent,
    timeTrendTileContent,
    cropSummaryTypeBarChartContent,
    hasPurchaseVolume,
  },
}: UseVolumeWeightedNetEFCardData) => {
  const {filtersState} = useGetKPIDashboardCommonFilters();
  const OverTimePanelComponent = useMemo(
    () => (
      <OverTimePanel
        primaryMetricTileContent={primaryMetricTileContent}
        timeTrendTileContent={timeTrendTileContent}
      />
    ),
    [primaryMetricTileContent, timeTrendTileContent]
  );

  const {
    PaginationComponent: ByCropPagination,
    SortComponent: ByCropSort,
    ChartComponent: ByCropChart,
  } = useSortedPaginatedSummaryTypeBarChart({
    isLoading,
    defaultSort: {by: 'VALUE', direction: 'ASC', valueSortDirection: 'ASC'},
    summaryTypeBarChartProps: cropSummaryTypeBarChartContent,
  });

  const panelsLookup = useMemo(
    () => ({
      [PANEL_CONTENT.ByYear.label]: {
        component: OverTimePanelComponent,
        ...PANEL_CONTENT.ByYear,
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByYear.title}
            body={PANEL_CONTENT.ByYear.tooltipContent}
          />
        ),
        isDisabledMessage: PANEL_CONTENT.ByYear.isDisabledMessage(filtersState, hasPurchaseVolume),
      },
      [PANEL_CONTENT.ByCrop.label]: {
        component: ByCropChart,
        pagination: ByCropPagination,
        menuItems: ByCropSort,
        ...PANEL_CONTENT.ByCrop,
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByCrop.title}
            body={PANEL_CONTENT.ByCrop.tooltipContent}
          />
        ),
        isDisabledMessage: PANEL_CONTENT.ByCrop.isDisabledMessage(filtersState, hasPurchaseVolume),
      },
    }),
    [
      OverTimePanelComponent,
      filtersState,
      hasPurchaseVolume,
      ByCropChart,
      ByCropPagination,
      ByCropSort,
    ]
  );

  return panelsLookup;
};
