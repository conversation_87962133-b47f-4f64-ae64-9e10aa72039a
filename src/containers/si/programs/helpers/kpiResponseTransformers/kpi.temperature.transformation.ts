import type {TemperatureKPIMetricData} from 'containers/si/api/apiTypes';
import {DEGREE} from 'containers/si/constants';
import {
  isValidTier,
  tierFormatter,
  tierUnit,
} from 'containers/si/programs/helpers/kpi.quantification_level.helpers';
import {formatAllNumbers} from 'containers/si/utils/formatters/number.format';
import {isFiniteNumber} from 'containers/si/utils/utils';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type TemperatureMetrics = {
  avgTemperature: Metric;
  avgMaxTemperature: Metric;
  yoyTemperatureDelta: Metric;
  tier: Metric;
};

export const private__degreesCelsiusUnit = (): UnitDetail<UnitType> => {
  return {
    unit: 'C' as const,
    unitName: {
      singular: `${DEGREE}C`,
      plural: `${DEGREE}C`,
      abbr: `${DEGREE}C`,
    },
  };
};

export const TEMPERATURE_METRICS_FORMATTER_MAP: Record<
  keyof TemperatureMetrics,
  (value: number) => string
> = {
  avgTemperature: (value: number) => formatAllNumbers(value, {sigDigits: 2}),
  avgMaxTemperature: (value: number) => formatAllNumbers(value, {sigDigits: 2}),
  yoyTemperatureDelta: (value: number) => formatAllNumbers(value, {sigDigits: 2}),
  tier: tierFormatter,
};

export const TEMPERATURE_METRICS_UNIT_MAP: Record<
  keyof TemperatureMetrics,
  () => UnitDetail<UnitType>
> = {
  avgTemperature: private__degreesCelsiusUnit,
  avgMaxTemperature: private__degreesCelsiusUnit,
  yoyTemperatureDelta: private__degreesCelsiusUnit,
  tier: tierUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeTemperatureMetrics);
 */
export const makeTemperatureMetrics = (
  data: TemperatureKPIMetricData
): TemperatureMetrics | null => {
  const {yoy_temperature_delta, avg_max_temperature, avg_temperature, quantification_level} = data;

  // These can be removed once a response deserializer and type validator is built
  // https://regrow.atlassian.net/browse/SI-1379
  // This method should handle
  // expected nulls or undefineds and
  // 0 and Finite checks as those are valid numbers
  if (
    !isFiniteNumber(avg_max_temperature) ||
    !isFiniteNumber(avg_temperature) ||
    !isFiniteNumber(yoy_temperature_delta) ||
    !isFiniteNumber(quantification_level) ||
    !isValidTier(quantification_level)
  ) {
    return null;
  }

  return {
    avgTemperature: {
      value: avg_temperature,
      unit: TEMPERATURE_METRICS_UNIT_MAP.avgTemperature().unit,
      formattedValue: TEMPERATURE_METRICS_FORMATTER_MAP.avgTemperature(avg_temperature),
    },
    avgMaxTemperature: {
      value: avg_max_temperature,
      unit: TEMPERATURE_METRICS_UNIT_MAP.avgMaxTemperature().unit,
      formattedValue: TEMPERATURE_METRICS_FORMATTER_MAP.avgMaxTemperature(avg_max_temperature),
    },
    yoyTemperatureDelta: {
      value: yoy_temperature_delta,
      unit: TEMPERATURE_METRICS_UNIT_MAP.yoyTemperatureDelta().unit,
      formattedValue: TEMPERATURE_METRICS_FORMATTER_MAP.yoyTemperatureDelta(yoy_temperature_delta),
    },
    tier: {
      value: quantification_level,
      unit: TEMPERATURE_METRICS_UNIT_MAP.tier().unit,
      formattedValue: TEMPERATURE_METRICS_FORMATTER_MAP.tier(quantification_level),
    },
  };
};
