import {isDefined} from '_common/utils/typeGuards';

export const VOLUME_WEIGHTED_SOC_EF_CONTENT = {
  name: (panelLabel?: string) =>
    `Field SOC sequestration factor ${
      isDefined(panelLabel) ? `${panelLabel} ` : ''
    }(volume-weighted)`,
  explainer: (panelLabel: string) => [
    `The average amount of SOC sequestered per kg yield ${panelLabel}, weighted by the volume purchased from each subregion. This is calculated by dividing the sum of the total SOC sequestered by the volume purchased in each subregion by the total volume purchased across all subregions.`,
    `Note that this datapoint can only be provided per commodity, is only useful when weighting volumes across multiple subregions, and is omitted for commodities/subregions/years without volumes.`,
  ],
  categoryColor: '2' as const,
};
