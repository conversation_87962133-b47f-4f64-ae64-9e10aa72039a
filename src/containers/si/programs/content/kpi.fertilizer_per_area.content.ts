import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {getUnit} from 'containers/si/utils/convert';

export const FERTILIZER_PER_AREA_CONTENT = {
  name: 'fertilizer application',
  explainer: (panelLabel: string) => (userUnitsSystem: MeasurementEnum) => [
    `Total fertilizer application (N, P, K, S) per ${
      getUnit(userUnitsSystem).unitName.singular
    } broken down ${panelLabel}.`,
    'Note that fertilizer application data will still be provided even if data for N, P, K, or S is missing.',
  ],
  categoryColor: '1' as const,
};
