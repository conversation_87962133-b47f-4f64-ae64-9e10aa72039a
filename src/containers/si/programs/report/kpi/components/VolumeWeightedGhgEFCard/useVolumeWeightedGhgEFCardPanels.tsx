import React, {useMemo} from 'react';

import {CardTooltip} from 'containers/si/programs/report/kpi/components/CardTooltip';
import {OverTimePanel} from 'containers/si/programs/report/kpi/components/OverTimePanel';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/VolumeWeightedGhgEFCard/content';
import {type UseVolumeWeightedGhgEFCardData} from 'containers/si/programs/report/kpi/components/VolumeWeightedGhgEFCard/useVolumeWeightedGhgEFCardData';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useSortedPaginatedSummaryTypeBarChart} from 'containers/si/programs/report/kpi/hooks/useSortedPaginatedSummaryTypeBarChart';

export const useVolumeWeightedGhgEFCardPanels = ({
  isLoading,
  data: {
    primaryMetricTileContent,
    timeTrendTileContent,
    cropSummaryTypeBarChartContent,
    hasPurchaseVolume,
  },
}: UseVolumeWeightedGhgEFCardData) => {
  const {filtersState} = useGetKPIDashboardCommonFilters();
  const OverTimePanelComponent = useMemo(
    () => (
      <OverTimePanel
        primaryMetricTileContent={primaryMetricTileContent}
        timeTrendTileContent={timeTrendTileContent}
      />
    ),
    [primaryMetricTileContent, timeTrendTileContent]
  );

  const {
    PaginationComponent: ByCropPagination,
    SortComponent: ByCropSort,
    ChartComponent: ByCropChart,
  } = useSortedPaginatedSummaryTypeBarChart({
    isLoading,
    defaultSort: {by: 'VALUE', direction: 'ASC', valueSortDirection: 'ASC'},
    summaryTypeBarChartProps: cropSummaryTypeBarChartContent,
  });

  const panelsLookup = useMemo(
    () => ({
      [PANEL_CONTENT.ByYear.label]: {
        component: OverTimePanelComponent,
        ...PANEL_CONTENT.ByYear,
        isDisabledMessage: PANEL_CONTENT.ByYear.isDisabledMessage(filtersState, hasPurchaseVolume),
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByYear.title}
            body={PANEL_CONTENT.ByYear.tooltipContent}
          />
        ),
      },
      [PANEL_CONTENT.ByCrop.label]: {
        component: ByCropChart,
        pagination: ByCropPagination,
        menuItems: ByCropSort,
        ...PANEL_CONTENT.ByCrop,
        isDisabledMessage: PANEL_CONTENT.ByCrop.isDisabledMessage(filtersState, hasPurchaseVolume),
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByCrop.title}
            body={PANEL_CONTENT.ByCrop.tooltipContent}
          />
        ),
      },
    }),
    [
      OverTimePanelComponent,
      filtersState,
      hasPurchaseVolume,
      ByCropChart,
      ByCropPagination,
      ByCropSort,
    ]
  );

  return panelsLookup;
};
