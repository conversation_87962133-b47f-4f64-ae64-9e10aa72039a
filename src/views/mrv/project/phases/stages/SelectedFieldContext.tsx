import {useQuery} from '@apollo/client';
import React, {createContext, useCallback, useContext, useEffect, useMemo} from 'react';
import type {FC} from 'react';
import {useHistory} from 'react-router-dom';

import type {Lab} from '@regrow-internal/design-system';

import type {GetProjectFarmsAndEnrolledFieldsQuery} from '__generated__/gql/graphql';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {isDefined, isEmptyObject} from '_common/utils/typeGuards';

import {GET_PROJECT_FARMS_AND_ENROLLED_FIELDS} from 'views/mrv/graphql/queries/projects';

type FarmsQueryResponse = NonNullable<
  NonNullable<GetProjectFarmsAndEnrolledFieldsQuery['mrv']['project']>['farms']
>;
export type FieldQueryResponse = NonNullable<NonNullable<FarmsQueryResponse[0]>['fields']>[0];

export type SelectedFieldEventContext = {
  field: FieldQueryResponse | null;
  events: Record<string, Array<Lab.GridRowId>> | null;
};
export type SelectedFieldContextType = {
  error: string | null;
  isLoading: boolean;
  farms: FarmsQueryResponse;
  selectedFarm: FarmsQueryResponse[0] | null;
  selectedField: FieldQueryResponse | null;
  setSelectedField: (fieldId: FieldQueryResponse['id']) => void;
  selectedFieldEvents: SelectedFieldEventContext;
  setSelectedFieldEvents: (events: Array<Lab.GridRowId>, stageType: string) => void;
  clearSelectedFieldEvents: () => void;
};

const initialState = {
  error: null,
  isLoading: true,
  farms: [],
  selectedFarm: null,
  selectedField: null,
  setSelectedField: () => undefined,
  selectedFieldEvents: {
    field: null,
    events: null,
  },
  setSelectedFieldEvents: () => undefined,
  clearSelectedFieldEvents: () => undefined,
};

const SelectedFieldContext = createContext<SelectedFieldContextType>(initialState);

export const SelectedFieldContextProvider: FC = ({children}) => {
  const {projectId} = useParsedMatchParams<{projectId: string}>();
  const history = useHistory();

  const [selectedFieldEvents, setSelectedFieldEvents] = React.useState<
    SelectedFieldContextType['selectedFieldEvents']
  >(initialState.selectedFieldEvents);

  const searchParams = useMemo(
    () => new URLSearchParams(history.location.search),
    [history.location.search]
  );

  // Selected farm and field
  const selectedFieldId = searchParams.get('fieldId');

  const setSelectedField = useCallback(
    (fieldId: FieldQueryResponse['id']) => {
      searchParams.set('fieldId', fieldId);
      history.push({
        search: searchParams.toString(),
      });
    },
    [history, searchParams]
  );

  // Farms queries
  const {
    data: farmsData,
    loading,
    error,
  } = useQuery(GET_PROJECT_FARMS_AND_ENROLLED_FIELDS, {
    variables: {projectId},
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
  });

  const farms = useMemo(() => farmsData?.mrv.project?.farms || [], [farmsData]);

  useEffect(() => {
    // If no field is selected, set the first field in the farms list as the selected field
    const firstField = farms?.[0]?.fields?.[0];

    if (selectedFieldId && firstField) {
      // if current selected field is not in the farms list, set the first field as selected
      if (!farms?.find(farm => farm.fields?.find(field => field.id === selectedFieldId))) {
        setSelectedField(firstField.id);
      }
    } else if (selectedFieldId === null && firstField) {
      setSelectedField(firstField.id);
    }
  }, [farms, selectedFieldId, setSelectedField]);

  const {selectedFarm, selectedField} = useMemo(() => {
    const foundFarm =
      selectedFieldId !== null
        ? farms?.find(farm => farm.fields?.find(field => field.id === selectedFieldId)) ?? null
        : null;
    const foundField =
      foundFarm !== null
        ? foundFarm.fields?.find(field => field.id === selectedFieldId) ?? null
        : null;

    return {selectedFarm: foundFarm, selectedField: foundField};
  }, [farms, selectedFieldId]);

  const handleSetSelectedEvents = useCallback(
    (events: Array<Lab.GridRowId>, stageType: string) => {
      if (isDefined(selectedFieldEvents.field) && selectedFieldEvents.field !== selectedField) {
        return;
      }
      setSelectedFieldEvents(previousState => {
        if (!events?.length) {
          const newEvents = previousState.events ? {...previousState.events} : {};
          if (newEvents[stageType]) {
            delete newEvents[stageType];
          }

          if (isEmptyObject(newEvents)) {
            return initialState.selectedFieldEvents;
          }

          return {
            field: selectedField,
            events: newEvents,
          };
        }

        return {
          field: selectedField,
          events: {
            ...previousState.events,
            [stageType]: events,
          },
        };
      });
    },
    [selectedField, selectedFieldEvents.field]
  );

  const selectedFieldContext = useMemo(
    () => ({
      error: error ? error.message : null,
      isLoading: loading,
      farms,
      selectedFarm,
      selectedField,
      setSelectedField,
      selectedFieldEvents,
      setSelectedFieldEvents: handleSetSelectedEvents,
      clearSelectedFieldEvents: () => {
        setSelectedFieldEvents(initialState.selectedFieldEvents);
      },
    }),
    [
      error,
      loading,
      farms,
      selectedFarm,
      selectedField,
      setSelectedField,
      selectedFieldEvents,
      handleSetSelectedEvents,
    ]
  );

  return (
    <SelectedFieldContext.Provider value={selectedFieldContext}>
      {children}
    </SelectedFieldContext.Provider>
  );
};

export const useSelectedFieldContext = () => {
  return useContext<SelectedFieldContextType>(SelectedFieldContext);
};
