import React, {useEffect} from 'react';
import {useIntl} from 'react-intl';

import {
  Alert,
  AlertTitle,
  Box,
  CircularProgress,
  SvgIcon,
  Typography,
} from '@regrow-internal/design-system';

import {useAppSelector} from 'store/useRedux';
import {RequestStatus} from 'types';

import {useMonitorPrefillImportFieldStatus} from 'containers/profile/fms-integration/hooks';

export const MonitorPrefillAlert: React.FC = () => {
  const {formatMessage} = useIntl();

  const {monitorImportIsComplete, fieldImportPercentageComplete} =
    useMonitorPrefillImportFieldStatus();

  const monitorImportStatus = useAppSelector(s => {
    return s.integrations.platforms.monitor;
  }).syncStatus;

  const [alertOpen, setAlertOpen] = React.useState(false);

  useEffect(() => {
    if (monitorImportStatus === RequestStatus.Loading) {
      setAlertOpen(true);
    }
  }, [monitorImportStatus]);

  const monitorMessage = React.useMemo(
    () => ({
      loading: (
        <>
          <AlertTitle>
            {formatMessage({
              id: 'MonitorApi.prefill.loading.title',
              defaultMessage: 'Detecting crop and tillage information.',
            })}
          </AlertTitle>
          {formatMessage({
            id: 'MonitorApi.prefill.loading.description',
            defaultMessage:
              'Remotely sensed data is being added to your cropping and tillage history.',
          })}
        </>
      ),
      success: (
        <AlertTitle>
          {formatMessage({
            id: 'MonitorApi.prefill.success',
            defaultMessage: 'Your crop and tillage information was successfully added!',
          })}
        </AlertTitle>
      ),
    }),
    [formatMessage]
  );

  const severity = monitorImportIsComplete ? 'success' : 'info';
  const icon = monitorImportIsComplete ? (
    <SvgIcon type="check-mark-circled" />
  ) : (
    <CircularProgress size={20} color="info" />
  );
  const alertContent = monitorImportIsComplete ? monitorMessage.success : monitorMessage.loading;

  const alertProps = monitorImportIsComplete
    ? {
        onClose: () => {
          setAlertOpen(false);
        },
      }
    : {};

  const loadingPercentage = (
    <Box px={2}>
      <Typography fontWeight={'bold'} fontSize="body1">
        {fieldImportPercentageComplete}%&nbsp;
        {formatMessage({id: 'Complete', defaultMessage: 'Complete'})}
      </Typography>
    </Box>
  );

  if (alertOpen) {
    return (
      <Alert
        {...alertProps}
        aria-label="Monitor prefill alert"
        severity={severity}
        icon={icon}
        sx={{
          '.MuiAlert-action': {
            alignItems: 'center',
          },
        }}
        {...(!monitorImportIsComplete && {
          action: loadingPercentage,
        })}
      >
        {alertContent}
      </Alert>
    );
  }

  return null;
};
