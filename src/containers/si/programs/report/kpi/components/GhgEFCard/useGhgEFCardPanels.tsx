import React, {useMemo} from 'react';

import {CardTooltip} from 'containers/si/programs/report/kpi/components/CardTooltip';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/GhgEFCard/content';
import {type UseGhgEFCardData} from 'containers/si/programs/report/kpi/components/GhgEFCard/useGhgEFCardData';
import {OverTimePanel} from 'containers/si/programs/report/kpi/components/OverTimePanel';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useSortedPaginatedSummaryTypeBarChart} from 'containers/si/programs/report/kpi/hooks/useSortedPaginatedSummaryTypeBarChart';

export const useGhgEFCardPanels = ({
  isLoading,
  data: {
    primaryMetricTileContent,
    timeTrendTileContent,
    cropSummaryTypeBarChartContent,
    subregionSummaryTypeBarChartContent,
  },
}: UseGhgEFCardData) => {
  const {filtersState} = useGetKPIDashboardCommonFilters();

  const OverTimePanelComponent = useMemo(
    () => (
      <OverTimePanel
        primaryMetricTileContent={primaryMetricTileContent}
        timeTrendTileContent={timeTrendTileContent}
      />
    ),
    [primaryMetricTileContent, timeTrendTileContent]
  );

  const {
    PaginationComponent: ByCropPagination,
    SortComponent: ByCropSort,
    ChartComponent: ByCropChart,
  } = useSortedPaginatedSummaryTypeBarChart({
    isLoading,
    defaultSort: {by: 'VALUE', direction: 'ASC', valueSortDirection: 'ASC'},
    summaryTypeBarChartProps: cropSummaryTypeBarChartContent,
  });

  const {
    PaginationComponent: BySubregionPagination,
    SortComponent: BySubregionSort,
    ChartComponent: BySubregionChart,
  } = useSortedPaginatedSummaryTypeBarChart({
    isLoading,
    defaultSort: {by: 'VALUE', direction: 'ASC', valueSortDirection: 'ASC'},
    summaryTypeBarChartProps: subregionSummaryTypeBarChartContent,
  });

  const panelsLookup = useMemo(
    () => ({
      [PANEL_CONTENT.ByYear.label]: {
        component: OverTimePanelComponent,
        ...PANEL_CONTENT.ByYear,
        isDisabledMessage: PANEL_CONTENT.ByYear.isDisabledMessage(filtersState),
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByYear.title}
            body={PANEL_CONTENT.ByYear.tooltipContent}
          />
        ),
      },
      [PANEL_CONTENT.ByCrop.label]: {
        component: ByCropChart,
        pagination: ByCropPagination,
        menuItems: ByCropSort,
        ...PANEL_CONTENT.ByCrop,
        isDisabledMessage: PANEL_CONTENT.ByCrop.isDisabledMessage(filtersState),
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByCrop.title}
            body={PANEL_CONTENT.ByCrop.tooltipContent}
          />
        ),
      },
      [PANEL_CONTENT.ByBoundary.label]: {
        component: BySubregionChart,
        pagination: BySubregionPagination,
        menuItems: BySubregionSort,
        ...PANEL_CONTENT.ByBoundary,
        isDisabledMessage: PANEL_CONTENT.ByBoundary.isDisabledMessage(filtersState),
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByBoundary.title}
            body={PANEL_CONTENT.ByBoundary.tooltipContent}
          />
        ),
      },
    }),
    [
      filtersState,
      ByCropChart,
      ByCropPagination,
      ByCropSort,
      BySubregionChart,
      BySubregionPagination,
      BySubregionSort,
      OverTimePanelComponent,
    ]
  );

  return panelsLookup;
};
