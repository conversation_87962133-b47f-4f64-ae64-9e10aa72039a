import React, {useEffect, useMemo} from 'react';
import {useIntl} from 'react-intl';

import {Alert, AlertTitle, Box, Skeleton, SvgIcon} from '@regrow-internal/design-system';

import {useFeatureEnabled} from 'containers/mrv/_hooks/useFeatures';
import {FEATURE_FLAGS} from 'containers/mrv/constants';
import {useFieldEventContext} from 'views/mrv/project/phases/stages/field-events/FieldEventContext';

export const IntendedPracticesPrefillAlert: React.FC = () => {
  const {fieldCultivationCycles, isLoading: fieldsDataLoading} = useFieldEventContext();
  const [alertOpen, setAlertOpen] = React.useState(false);
  const intl = useIntl();
  const isIntendedPrefillEnabled = useFeatureEnabled(FEATURE_FLAGS.INTENDED_PRACTICES_PREFILL);

  const containsIntendedPracticesPrefill = useMemo(() => {
    if (!fieldsDataLoading) {
      return fieldCultivationCycles.some(cc => cc.contains_prefilled_monitoring_phase_events);
    }
  }, [fieldCultivationCycles, fieldsDataLoading]);

  useEffect(() => {
    if (containsIntendedPracticesPrefill) {
      setAlertOpen(true);
    }
  }, [containsIntendedPracticesPrefill]);

  if (!alertOpen && isIntendedPrefillEnabled && fieldsDataLoading) {
    return <Skeleton height={50} />;
  }

  if (alertOpen) {
    return (
      <Box mt={5}>
        <Alert
          aria-label="Intended Practices prefill alert"
          severity={'info'}
          icon={<SvgIcon type="info-circled" />}
          title="We’ve prefilled some of your data."
          onClose={() => {
            setAlertOpen(false);
          }}
        >
          <AlertTitle>
            {intl.formatMessage({
              id: 'IntendedPractices.prefill.title',
              defaultMessage: 'We’ve prefilled some of your data.',
            })}
          </AlertTitle>
          {intl.formatMessage({
            id: 'IntendedPractices.prefill.description',
            defaultMessage:
              'Based on your enrollment intentions, commodities and practices have been added for you. Please review and update as needed.',
          })}
        </Alert>
      </Box>
    );
  }

  return null;
};
