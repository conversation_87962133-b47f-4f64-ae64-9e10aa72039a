import {parseISO} from 'date-fns';
import React from 'react';

import {Button, IconButton, SvgIcon, useTheme} from '@regrow-internal/design-system';

import {LeafDatePicker} from '_common/components/LeafDatePicker';
import {isDefined, isTruthy} from '_common/utils/typeGuards';

import {ErrorToolTipWrapper} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/ErrorToolTipWrapper';
import type {
  TableDateInputProps,
  TableInputProps,
} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/types';
import {isStrictlyValidWithDateFns} from 'views/mrv/project/phases/stages/field-events/utils/dateUtils';

import {inputHasValue} from './utils';

export const DateInput = ({
  value,
  onChange,
  error,
  required,
  name,
  min,
  max,
  color,
  inputRef,
  referenceDate = null,
  formatMessage,
  ...props
}: TableInputProps & TableDateInputProps) => {
  const {'aria-label': ariaLabel} = props;
  const muiTheme = useTheme();
  const isRequired = !inputHasValue(value) && required;
  const isError = inputHasValue(value) && isDefined(error) && isTruthy(error);

  const muiThemeSurface = muiTheme.palette.semanticPalette.surface;
  const valueToDate = isDefined(value) ? new Date(value) : null;

  const minDateToISO = min ? parseISO(min) : null;
  const maxDateToISO = max ? parseISO(max) : null;
  const [minMaxError, setMinMaxError] = React.useState<string>('');

  const resolvedError = minMaxError
    ? formatMessage({id: 'DateInput.OutOfRange', defaultMessage: 'Date is out of range.'})
    : error || '';

  return (
    <ErrorToolTipWrapper error={resolvedError}>
      <LeafDatePicker
        views={['year', 'month', 'day']}
        openTo="month"
        slotProps={{
          textField: {
            variant: 'filled',
            error: !!minMaxError || isError,
            color,
            required,
            fullWidth: true,
            sx: {
              backgroundColor:
                minMaxError || isError
                  ? muiThemeSurface.error
                  : isRequired
                  ? muiThemeSurface.warning
                  : 'transparent',
            },
            inputProps: {
              'aria-label': ariaLabel,
              min: minDateToISO,
              max: maxDateToISO,
              name,
              color,
            },
          },
          popper: {
            sx: {
              zIndex: muiTheme.zIndex.modal + 500,
            },
          },
        }}
        referenceDate={referenceDate}
        minDate={minDateToISO}
        maxDate={maxDateToISO}
        inputRef={inputRef}
        onChange={(newValue, {validationError}) => {
          if (validationError) {
            const isValid = newValue && isStrictlyValidWithDateFns(newValue);
            if (isValid && (validationError === 'minDate' || validationError === 'maxDate')) {
              setMinMaxError(validationError);
            } else {
              setMinMaxError('');
            }

            return;
          }
          onChange(newValue);
        }}
        format="yyyy-MM-dd"
        value={valueToDate}
        slots={{
          switchViewIcon: __props => (
            <SvgIcon className={__props.className} color="main" type="chevron-down" />
          ),
          openPickerButton: __props => (
            <IconButton onClick={__props.onClick} color="primary">
              <SvgIcon type="calendar" />
            </IconButton>
          ),
          nextIconButton: __props => (
            <Button onClick={__props.onClick} variant="outlined" color="secondary">
              <SvgIcon type="chevron-right" />
            </Button>
          ),
          previousIconButton: __props => (
            <Button onClick={__props.onClick} variant="outlined" color="secondary">
              <SvgIcon type="chevron-left" />
            </Button>
          ),
        }}
      />
    </ErrorToolTipWrapper>
  );
};
