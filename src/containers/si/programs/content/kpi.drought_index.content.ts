export const DROUGHT_CONTENT = {
  name: 'drought severity',
  explainer: (panelLabel: string) => [
    `The annual ERA5 drought severity classification broken down ${panelLabel}.`,
    'Standard precipitation index (SPI) is averaged over the specified geography to represent a drought severity classification for the given area.',
    'The drought severity classifications are defined as follows:',
    'Normal / wet: No drought (SPI ≥ -0.49)\nD0: Abnormally dry (SPI -0.5 to -0.79)\nD1: Moderate drought (SPI -0.8 to -1.29)\nD2: Severe drought (SPI -1.3 to -1.59)\nD3: Extreme drought (SPI -1.6 to -1.99)\nD4: Exceptional drought (SPI ≤ -2.0)',
    'Note: If field level data is available for the entire specified geography, the drought severity classification represents the average SPI over just the areas which grow the specified commodities. Otherwise, the drought severity classification represents the average SPI over the entire geography.',
  ],
  categoryColor: '4' as const,
};
