import {NotificationLevel} from 'views/mrv/project/phases/stages/notifications/types';
import type {
  FieldNotifications,
  NotificationLookup,
} from 'views/mrv/project/phases/stages/notifications/types';
import {
  clearFieldNotificationState,
  clearPhaseNotificationState,
} from 'views/mrv/project/phases/stages/notifications/utils';

describe('Notification Utils', () => {
  describe('clearFieldNotificationState', () => {
    const mockFieldNotifications: FieldNotifications = {
      'field-1': {
        [NotificationLevel.SUCCESS]: [
          {id: 'success-1', title: 'Success 1', message: 'First success'},
          {id: 'success-2', title: 'Success 2', message: 'Second success'},
        ],
        [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
      },
      'field-2': {
        [NotificationLevel.WARNING]: [
          {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
        ],
      },
    };

    it('should return unchanged state when field does not exist', () => {
      const result = clearFieldNotificationState(mockFieldNotifications, 'non-existent-field');
      expect(result).toEqual(mockFieldNotifications);
    });

    it('should clear all notifications for a field when no level specified', () => {
      const result = clearFieldNotificationState(mockFieldNotifications, 'field-1');

      expect(result).toEqual({
        'field-2': {
          [NotificationLevel.WARNING]: [
            {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
          ],
        },
      });
    });

    it('should return unchanged state when level does not exist for field', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-1',
        NotificationLevel.WARNING
      );
      expect(result).toEqual(mockFieldNotifications);
    });

    it('should clear all notifications for a specific level', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-1',
        NotificationLevel.SUCCESS
      );

      expect(result).toEqual({
        'field-1': {
          [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
        },
        'field-2': {
          [NotificationLevel.WARNING]: [
            {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
          ],
        },
      });
    });

    it('should remove field entirely when clearing last level', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-2',
        NotificationLevel.WARNING
      );

      expect(result).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: [
            {id: 'success-1', title: 'Success 1', message: 'First success'},
            {id: 'success-2', title: 'Success 2', message: 'Second success'},
          ],
          [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
        },
      });
    });

    it('should clear specific notification by ID', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-1',
        NotificationLevel.SUCCESS,
        'success-1'
      );

      expect(result).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: [
            {id: 'success-2', title: 'Success 2', message: 'Second success'},
          ],
          [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
        },
        'field-2': {
          [NotificationLevel.WARNING]: [
            {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
          ],
        },
      });
    });

    it('should remove level when clearing last notification by ID', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-1',
        NotificationLevel.ERROR,
        'error-1'
      );

      expect(result).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: [
            {id: 'success-1', title: 'Success 1', message: 'First success'},
            {id: 'success-2', title: 'Success 2', message: 'Second success'},
          ],
        },
        'field-2': {
          [NotificationLevel.WARNING]: [
            {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
          ],
        },
      });
    });

    it('should remove field entirely when clearing last notification by ID', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-2',
        NotificationLevel.WARNING,
        'warning-1'
      );

      expect(result).toEqual({
        'field-1': {
          [NotificationLevel.SUCCESS]: [
            {id: 'success-1', title: 'Success 1', message: 'First success'},
            {id: 'success-2', title: 'Success 2', message: 'Second success'},
          ],
          [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
        },
      });
    });

    it('should return unchanged state when notification ID does not exist', () => {
      const result = clearFieldNotificationState(
        mockFieldNotifications,
        'field-1',
        NotificationLevel.SUCCESS,
        'non-existent-id'
      );

      expect(result).toEqual(mockFieldNotifications);
    });

    it('should handle empty state', () => {
      const emptyState: FieldNotifications = {};
      const result = clearFieldNotificationState(emptyState, 'field-1');
      expect(result).toEqual(emptyState);
    });
  });

  describe('clearPhaseNotificationState', () => {
    const mockPhaseNotifications: NotificationLookup = {
      [NotificationLevel.SUCCESS]: [
        {id: 'success-1', title: 'Success 1', message: 'First success'},
        {id: 'success-2', title: 'Success 2', message: 'Second success'},
      ],
      [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
      [NotificationLevel.WARNING]: [
        {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
      ],
    };

    it('should clear all notifications when no level specified', () => {
      const result = clearPhaseNotificationState(mockPhaseNotifications);
      expect(result).toEqual({});
    });

    it('should return unchanged state when level does not exist', () => {
      const stateWithoutInfo = {
        [NotificationLevel.SUCCESS]: [
          {id: 'success-1', title: 'Success 1', message: 'First success'},
        ],
      };

      const result = clearPhaseNotificationState(stateWithoutInfo, NotificationLevel.ERROR);
      expect(result).toEqual(stateWithoutInfo);
    });

    it('should clear all notifications for a specific level', () => {
      const result = clearPhaseNotificationState(mockPhaseNotifications, NotificationLevel.SUCCESS);

      expect(result).toEqual({
        [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
        [NotificationLevel.WARNING]: [
          {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
        ],
      });
    });

    it('should clear specific notification by ID', () => {
      const result = clearPhaseNotificationState(
        mockPhaseNotifications,
        NotificationLevel.SUCCESS,
        'success-1'
      );

      expect(result).toEqual({
        [NotificationLevel.SUCCESS]: [
          {id: 'success-2', title: 'Success 2', message: 'Second success'},
        ],
        [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
        [NotificationLevel.WARNING]: [
          {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
        ],
      });
    });

    it('should remove level when clearing last notification by ID', () => {
      const result = clearPhaseNotificationState(
        mockPhaseNotifications,
        NotificationLevel.ERROR,
        'error-1'
      );

      expect(result).toEqual({
        [NotificationLevel.SUCCESS]: [
          {id: 'success-1', title: 'Success 1', message: 'First success'},
          {id: 'success-2', title: 'Success 2', message: 'Second success'},
        ],
        [NotificationLevel.WARNING]: [
          {id: 'warning-1', title: 'Warning 1', message: 'First warning'},
        ],
      });
    });

    it('should return unchanged state when notification ID does not exist', () => {
      const result = clearPhaseNotificationState(
        mockPhaseNotifications,
        NotificationLevel.SUCCESS,
        'non-existent-id'
      );

      expect(result).toEqual(mockPhaseNotifications);
    });

    it('should handle empty state', () => {
      const emptyState: NotificationLookup = {};
      const result = clearPhaseNotificationState(emptyState);
      expect(result).toEqual({});
    });

    it('should handle state with empty level arrays', () => {
      const stateWithEmptyLevel: NotificationLookup = {
        [NotificationLevel.SUCCESS]: [],
        [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
      };

      const result = clearPhaseNotificationState(stateWithEmptyLevel, NotificationLevel.SUCCESS);
      expect(result).toEqual({
        [NotificationLevel.ERROR]: [{id: 'error-1', title: 'Error 1', message: 'First error'}],
      });
    });
  });
});
