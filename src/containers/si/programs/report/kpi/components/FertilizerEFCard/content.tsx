import {FERT_EF_CONTENT} from 'containers/si/programs/content/kpi.fert_emissions_factor.content';
import {
  FERT_EF_METRICS_FORMATTER_MAP,
  FERT_EF_METRICS_UNIT_MAP,
  makeFertEFMetrics,
  type FertEFMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fert_emissions_factor.transformation';
import {
  NO_DATA_FOR_MULTIPLE_CROPS_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

export const KPI = 'fert_emissions_factor';
export const FORMATTER_MAP = FERT_EF_METRICS_FORMATTER_MAP;
export const UNIT_MAP = FERT_EF_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<FertEFMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'fertEmissionsPerYield',
};
export const TRANSFORMER = makeFertEFMetrics;
export const CATEGORY_COLOR_KEY = FERT_EF_CONTENT.categoryColor;

const CARD_CONTENT = {
  title: FERT_EF_CONTENT.name,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
    isDownwardChangePositive: true,
    tooltipContent: FERT_EF_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    isDisabledMessage: (_filters: KPIDataFiltersState) => undefined,
    fetchSummary: 'crop_type',
    tooltipContent: FERT_EF_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'boundary',
    tooltipContent: FERT_EF_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
