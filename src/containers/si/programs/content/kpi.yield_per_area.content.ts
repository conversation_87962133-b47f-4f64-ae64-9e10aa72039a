import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {getUnit} from 'containers/si/utils/convert';

export const YIELD_PER_AREA_CONTENT = {
  name: 'crop yield',
  explainer: (panelLabel: string) => (userUnitsSystem: MeasurementEnum) => [
    `The average mass of commodities harvested per ${
      getUnit(userUnitsSystem).unitName.singular
    } broken down ${panelLabel}. The denominator is the total cropland ${
      getUnit(userUnitsSystem).unitName.plural
    } growing the given crop.`,
    `Note that yield will only be provided per commodity.`,
  ],
  categoryColor: '8' as const,
};
