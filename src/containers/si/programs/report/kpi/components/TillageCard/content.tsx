import React from 'react';

import {Typography} from '@regrow-internal/design-system';

import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {capitalizeFirstLetter} from '_common/utils/string';

import {TILLAGE_CONTENT} from 'containers/si/programs/content/kpi.tillage.content';
import {
  TILLAGEMETRICS_FORMATTER_MAP,
  TILLAGEMETRICS_UNIT_MAP,
  type TillageMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.tillage.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import {getUnit} from 'containers/si/utils/convert';

export const KPI = 'tillage';
export const FORMATTER_MAP = TILLAGEMETRICS_FORMATTER_MAP;
export const UNIT_MAP = TILLAGEMETRICS_UNIT_MAP;
export const METRIC_MAP: Pick<
  MetricLookupMap<TillageMetrics>,
  | 'primaryMetricKey'
  | 'secondaryMetricKey'
  | 'uncertaintyMetricKey'
  | 'secondaryUncertaintyMetricKey'
  | 'trackedMetricKey'
  | 'unknownMetricKey'
> = {
  primaryMetricKey: 'adoption',
  uncertaintyMetricKey: 'adoptionStdDev',
  secondaryMetricKey: 'conservationTillageArea',
  secondaryUncertaintyMetricKey: 'conservationTillageAreaStdDev',
  trackedMetricKey: 'totalTrackedArea',
  unknownMetricKey: 'unknownArea',
};
export const CATEGORY_COLOR_KEY = TILLAGE_CONTENT.categoryColor;

const PANEL_LABEL_BY_TILLAGE_TYPE = 'by classification';

const CARD_CONTENT = {
  title: capitalizeFirstLetter(TILLAGE_CONTENT.name),
} as const;

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: TILLAGE_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: TILLAGE_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: TILLAGE_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
  ByType: {
    title: `Tillage ${PANEL_LABEL_BY_TILLAGE_TYPE} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL_BY_TILLAGE_TYPE,
    tooltipContent: (userUnitsSystem: MeasurementEnum) => (
      <>
        <Typography variant="body2">
          {`The number of ${
            getUnit(userUnitsSystem).unitName.plural
          } practicing no till, reduced till, or conventional till broken down ${
            PANEL_LABEL.OVER_TIME
          }. Remaining unverified ${getUnit(userUnitsSystem).unitName.plural}  (${
            getUnit(userUnitsSystem).unitName.plural
          } that we cannot confidently determine tillage practices on through remote-sensing) are also displayed on this chart.`}
        </Typography>
      </>
    ),
  },
} as const;
