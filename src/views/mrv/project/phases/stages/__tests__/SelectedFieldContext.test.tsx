import type { MockedProviderProps } from '@apollo/client/testing';
import { MockedProvider } from '@apollo/client/testing';
import { act, waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';
import type { ReactNode } from 'react';
import React from 'react';
import { RawIntlProvider } from 'react-intl';
import { BrowserRouter, useHistory } from 'react-router-dom';



import { StageTypes } from '__generated__/mrv/mrvApi.types';
import { mockIntl } from '_common/test_utils/renderWithProviders';



import { ERROR_FARMS_AND_FIELDS_MOCK, GET_FARMS_AND_ENROLLED_FIELDS_MOCK, getFieldCultivationCycleEventsMock } from 'views/mrv/project/phases/stages/__tests__/mock-data/graphqlMocks';
import { mockFieldId, mockProjectFarms } from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import { SelectedFieldContextProvider, useSelectedFieldContext } from 'views/mrv/project/phases/stages/SelectedFieldContext';





jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));
jest.mock('views/mrv/project/phases/stages/StageContext', () => ({
  useStageContext: () => ({currentStage: {id: '14989', type: StageTypes.IRRIGATION_EVENTS}}),
}));
jest.mock('react-router-dom', () => {
  return {
    ...jest.requireActual('react-router-dom'),
    useHistory: jest.fn(),
  };
});

const renderTestComponent = (mocks: MockedProviderProps['mocks'], children: ReactNode) => {
  return (
    <RawIntlProvider value={mockIntl}>
      <BrowserRouter>
        <MockedProvider mocks={mocks}>
          <SelectedFieldContextProvider>{children}</SelectedFieldContextProvider>
        </MockedProvider>
      </BrowserRouter>
    </RawIntlProvider>
  );
};

const GET_FIELD_CULTIVATION_CYCLE_EVENTS_MOCK = getFieldCultivationCycleEventsMock(mockFieldId);
const graphqlMocks = [
  GET_FARMS_AND_ENROLLED_FIELDS_MOCK,
  GET_FIELD_CULTIVATION_CYCLE_EVENTS_MOCK,
  GET_FARMS_AND_ENROLLED_FIELDS_MOCK,
];

describe('SelectedFieldContext', () => {
  const historyMock = useHistory as jest.MockedFunction<any>;

  beforeAll(() => {
    historyMock.mockReturnValue({
      push: jest.fn(),
      location: {search: `fieldId=${mockFieldId}`},
    });
  });

  afterEach(() => jest.clearAllMocks());

  it('should render loading state', async () => {
    const {result} = renderHook(() => useSelectedFieldContext(), {
      wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
    });
    await waitFor(() => {
      expect(result.current.isLoading).toEqual(true);
    });
  });

  it('should render error state', async () => {
    const {result} = renderHook(() => useSelectedFieldContext(), {
      wrapper: ({children}) =>
        renderTestComponent([ERROR_FARMS_AND_FIELDS_MOCK, ...graphqlMocks], children),
    });
    await waitFor(() => {
      expect(result.current.error).toEqual('An error occurred');
    });
  });

  describe('Selected field', () => {
    it('should return list of farms with enrolled fields', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.farms).toMatchObject(
          mockProjectFarms.map(farm => ({
            ...farm,
            fields: farm.fields.map(field => ({id: field.id, name: field.name})),
          }))
        );
      });
    });

    it('should set the selected field from search param', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toEqual(
          expect.objectContaining({
            id: mockFieldId,
          })
        );
      });
    });

    it('should set the selected selected farm based on the selected field', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedFarm).toEqual(
          expect.objectContaining({
            name: 'Canada farm',
            id: '139123',
          })
        );
      });
    });

    it('should toggle the selected field and set in search state', async () => {
      const mockedPush = jest.fn();
      historyMock.mockReturnValue({
        push: mockedPush,
        location: {search: `fieldId=${mockFieldId}`},
      });
      const newFieldId = '108264';
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) =>
          renderTestComponent(
            [...graphqlMocks, getFieldCultivationCycleEventsMock(newFieldId)],
            children
          ),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toEqual(
          expect.objectContaining({
            id: mockFieldId,
          })
        );
      });

      act(() => {
        result.current.setSelectedField(newFieldId);
      });
      await waitFor(() => {
        expect(mockedPush).toHaveBeenCalledWith(
          expect.objectContaining({search: `fieldId=${newFieldId}`})
        );
      });
    });
  });

  describe('Selected field events', () => {
    it('should initialize with empty selectedFieldEvents', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents).toEqual({
          field: null,
          events: null,
        });
      });
    });

    it('should set selected events for a field', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const eventIds = ['event-1', 'event-2', 'event-3'];
      const eventType = 'CroppingEvent';

      act(() => {
        result.current.setSelectedFieldEvents(eventIds, eventType);
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents).toEqual({
          field: result.current.selectedField,
          events: {
            [eventType]: eventIds,
          },
        });
      });
    });

    it('should update events for the same field and event type', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const initialEventIds = ['event-1', 'event-2'];
      const updatedEventIds = ['event-1', 'event-2', 'event-3', 'event-4'];
      const eventType = 'CroppingEvent';

      // Set initial events
      act(() => {
        result.current.setSelectedFieldEvents(initialEventIds, eventType);
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events?.[eventType]).toEqual(initialEventIds);
      });

      act(() => {
        result.current.setSelectedFieldEvents(updatedEventIds, eventType);
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events?.[eventType]).toEqual(updatedEventIds);
      });
    });

    it('should handle multiple stage types for the same field', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const croppingEventIds = ['crop-1', 'crop-2'];
      const tillageEventIds = ['tillage-1', 'tillage-2', 'tillage-3'];

      act(() => {
        result.current.setSelectedFieldEvents(croppingEventIds, 'CROP_EVENTS');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual({
          CROP_EVENTS: croppingEventIds,
        });
      });

      act(() => {
        result.current.setSelectedFieldEvents(tillageEventIds, 'TILLAGE_EVENTS');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual({
          CROP_EVENTS: croppingEventIds,
          TILLAGE_EVENTS: tillageEventIds,
        });
      });
    });

    it('should clear events for a specific stage type when empty array is passed', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const croppingEventIds = ['crop-1', 'crop-2'];
      const tillageEventIds = ['tillage-1', 'tillage-2'];

      act(() => {
        result.current.setSelectedFieldEvents(croppingEventIds, 'CROP_EVENTS');
      });
      act(() => {
        result.current.setSelectedFieldEvents(tillageEventIds, 'TILLAGE_EVENTS');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual({
          CROP_EVENTS: croppingEventIds,
          TILLAGE_EVENTS: tillageEventIds,
        });
      });

      // Clear cropping events
      act(() => {
        result.current.setSelectedFieldEvents([], 'CROP_EVENTS');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual({
          TILLAGE_EVENTS: tillageEventIds,
        });
      });
    });

    it('should ignore setting events when events for a different field already exist', async () => {
      const {result, rerender} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const originalField = result.current.selectedField;
      const eventIds = ['event-1', 'event-2'];

      act(() => {
        result.current.setSelectedFieldEvents(eventIds, 'CroppingEvent');
      });

      const newFieldId = '108264';
      act(() => {
        result.current.setSelectedField(newFieldId);
      });
      rerender();

      await waitFor(() => {
        expect(result.current.selectedFieldEvents).toEqual({
          field: originalField,
          events: {
            CroppingEvent: eventIds,
          },
        });
      });

      const newEventIds = ['new-event-1'];
      act(() => {
        result.current.setSelectedFieldEvents(newEventIds, 'TillageEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents).toEqual({
          field: originalField,
          events: {
            CroppingEvent: eventIds,
          },
        });
      });
    });

    it('should clear all selected events when clearSelectedFieldEvents is called', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const croppingEventIds = ['crop-1', 'crop-2'];
      const tillageEventIds = ['tillage-1', 'tillage-2'];

      act(() => {
        result.current.setSelectedFieldEvents(croppingEventIds, 'CroppingEvent');
      });
      act(() => {
        result.current.setSelectedFieldEvents(tillageEventIds, 'TillageEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual({
          CroppingEvent: croppingEventIds,
          TillageEvent: tillageEventIds,
        });
      });

      act(() => {
        result.current.clearSelectedFieldEvents();
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents).toEqual({
          field: null,
          events: null,
        });
      });
    });

    it('should handle clearing the last event type correctly', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const eventIds = ['event-1', 'event-2'];

      act(() => {
        result.current.setSelectedFieldEvents(eventIds, 'CroppingEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual({
          CroppingEvent: eventIds,
        });
      });

      act(() => {
        result.current.setSelectedFieldEvents([], 'CroppingEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.events).toEqual(null);
      });
    });

    it('should preserve field reference when updating events for the same field', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      await waitFor(() => {
        expect(result.current.selectedField).toBeTruthy();
      });

      const field = result.current.selectedField;
      const eventIds = ['event-1', 'event-2'];

      act(() => {
        result.current.setSelectedFieldEvents(eventIds, 'CroppingEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.field).toBe(field);
      });

      const updatedEventIds = ['event-1', 'event-2', 'event-3'];
      act(() => {
        result.current.setSelectedFieldEvents(updatedEventIds, 'CroppingEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.field).toBe(field);
        expect(result.current.selectedFieldEvents.events?.CroppingEvent).toEqual(updatedEventIds);
      });
    });

    it('should clear field when updating events removes all events', async () => {
      const {result} = renderHook(() => useSelectedFieldContext(), {
        wrapper: ({children}) => renderTestComponent(graphqlMocks, children),
      });

      const eventIds = ['event-1', 'event-2'];
      act(() => {
        result.current.setSelectedFieldEvents(eventIds, 'CroppingEvent');
      });

      await waitFor(() => {
        expect(result.current.selectedFieldEvents.field).toBe(result.current.selectedField);
      });

      act(() => {
        result.current.setSelectedFieldEvents([], 'CroppingEvent');
      });

      expect(result.current.selectedFieldEvents.field).toBeNull();
    });
  });
});