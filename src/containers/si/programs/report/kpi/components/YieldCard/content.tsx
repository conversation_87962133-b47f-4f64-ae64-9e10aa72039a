import {capitalizeFirstLetter} from '_common/utils/string';

import {YIELD_PER_AREA_CONTENT} from 'containers/si/programs/content/kpi.yield_per_area.content';
import {
  makeYieldPerAreaMetrics,
  YIELD_PER_AREA_METRICS_FORMATTER_MAP,
  YIELD_PER_AREA_METRICS_UNIT_MAP,
  type YieldPerAreaMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.yield_per_area.transformation';
import {
  NO_DATA_FOR_MULTIPLE_CROPS_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';

export const KPI = 'yield_per_area';
export const FORMATTER_MAP = YIELD_PER_AREA_METRICS_FORMATTER_MAP;
export const UNIT_MAP = YIELD_PER_AREA_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<YieldPerAreaMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'yieldPerArea',
};
export const TRANSFORMER = makeYieldPerAreaMetrics;
export const CATEGORY_COLOR_KEY = YIELD_PER_AREA_CONTENT.categoryColor;

const CARD_CONTENT = {
  title: capitalizeFirstLetter(YIELD_PER_AREA_CONTENT.name),
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
    tooltipContent: YIELD_PER_AREA_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    isDisabledMessage: (_filters: KPIDataFiltersState) => undefined,
    fetchSummary: 'crop_type',
    tooltipContent: YIELD_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltip: 'Yield per area by subregion for your selected filters',
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'boundary',
    tooltipContent: YIELD_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
