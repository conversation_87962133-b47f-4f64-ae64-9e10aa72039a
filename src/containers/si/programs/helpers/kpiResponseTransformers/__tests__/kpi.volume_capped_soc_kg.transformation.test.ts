import {KPIVolumeCappedSocKgSummarizeByCropTypeMock} from 'containers/si/__mocks__/KPIVolumeCappedSocMetricsMock';
import {makeVolumeCappedSocMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.volume_capped_soc_kg.transformation';

describe('makeVolumeCappedSocMetrics', () => {
  it('Should return VolumeCappedSocMetrics for a crop_type_summary with appropriate units', () => {
    expect(
      makeVolumeCappedSocMetrics(KPIVolumeCappedSocKgSummarizeByCropTypeMock.crop_type_summary[1]!)
    ).toEqual({volumeCappedSocMass: {formattedValue: '0.01', unit: 'mt', value: 0.01}});
    expect(
      makeVolumeCappedSocMetrics(KPIVolumeCappedSocKgSummarizeByCropTypeMock.crop_type_summary[2]!)
    ).toEqual({volumeCappedSocMass: {formattedValue: '0.02', unit: 'mt', value: 0.02}});
    expect(
      makeVolumeCappedSocMetrics(KPIVolumeCappedSocKgSummarizeByCropTypeMock.crop_type_summary[3]!)
    ).toEqual({volumeCappedSocMass: {formattedValue: '0.03', unit: 'mt', value: 0.03}});
  });
  it('Should return null when null parameters are provided', () => {
    expect(
      makeVolumeCappedSocMetrics({
        //@ts-expect-error covering all nil cases
        volume_weighted_soc_kg: null,
      })
    ).toEqual(null);
    expect(
      makeVolumeCappedSocMetrics({
        volume_weighted_soc_kg: undefined,
      })
    ).toEqual(null);
    expect(makeVolumeCappedSocMetrics({})).toEqual(null);
  });

  it('Should return null when non finite parameters are provided', () => {
    expect(
      makeVolumeCappedSocMetrics({
        volume_weighted_soc_kg: Infinity,
      })
    ).toEqual(null);
  });
});
