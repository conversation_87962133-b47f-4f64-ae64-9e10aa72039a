import {capitalizeFirstLetter} from '_common/utils/string';

import {COVER_CROP_CONTENT} from 'containers/si/programs/content/kpi.cover_crop.content';
import {
  COVERCROPMETRICS_FORMATTER_MAP,
  COVERCROPMETRICS_UNIT_MAP,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.cover_crop.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';

export const KPI = 'cover_crop';
export const FORMATTER_MAP = COVERCROPMETRICS_FORMATTER_MAP;
export const UNIT_MAP = COVERCROPMETRICS_UNIT_MAP;
export const CATEGORY_COLOR_KEY = COVER_CROP_CONTENT.categoryColor;

export const CARD_CONTENT = {
  title: capitalizeFirstLetter(COVER_CROP_CONTENT.name),
};

export const PANEL_CONTENT = {
  ByYear: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: COVER_CROP_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
  },
  ByCrop: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: COVER_CROP_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
  },
  ByBoundary: {
    title: `${CARD_CONTENT.title} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: COVER_CROP_CONTENT.explainer(PANEL_LABEL.BY_AREA),
  },
} as const;
