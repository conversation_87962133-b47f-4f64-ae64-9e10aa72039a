import type {ReactNode} from 'react';
import React from 'react';

import {Box, Typography, useTheme} from '@regrow-internal/design-system';

import type {Phase} from '__generated__/gql/graphql';
import {PhaseTypes} from '__generated__/mrv/mrvApi.types';

import {IntendedPracticesPrefillAlert} from 'views/mrv/project/phases/stages/notifications/alerts/IntendedPracticesPrefillAlert';
import {MonitorPrefillAlert} from 'views/mrv/project/phases/stages/notifications/alerts/MonitorPrefillAlert';
import {PhaseAlert} from 'views/mrv/project/phases/stages/notifications/alerts/PhaseAlert';

import {FieldEventsTableWrapper} from './field-event-table/table-components/FieldEventsTableWrapper';
import {FieldEventContextProvider} from './FieldEventContext';

type FieldEventsStageWrapperProps = {
  title: string;
  description: ReactNode;
  children: ReactNode;
  currentPhase: Phase['type'] | undefined;
};

export const FieldEventsStageWrapper: React.FC<FieldEventsStageWrapperProps> = ({
  title,
  description,
  children,
  currentPhase,
}) => {
  const theme = useTheme();

  return (
    <>
      <Box maxWidth={theme.fixedWidths.md}>
        <Typography variant="h2">{title}</Typography>
        <Typography variant="body1">{description}</Typography>
      </Box>
      {currentPhase === PhaseTypes.ENROLMENT && <MonitorPrefillAlert />}
      <FieldEventContextProvider>
        <IntendedPracticesPrefillAlert />
      </FieldEventContextProvider>
      <PhaseAlert />
      <FieldEventsTableWrapper>{children}</FieldEventsTableWrapper>
    </>
  );
};
