import { fireEvent, screen } from '@testing-library/react';
import React from 'react';

import { renderWithSimpleProviders } from '_common/test_utils/renderWithProviders';

import { PhaseAlert } from '../PhaseAlert';
import { useNotificationContext } from '../../NotificationContext';
import { NotificationLevel } from '../../types';

jest.mock('../../NotificationContext', () => ({
  ...jest.requireActual('../../NotificationContext'),
  useNotificationContext: jest.fn(),
}));

const mockUseNotificationContext = useNotificationContext as jest.MockedFunction<
  typeof useNotificationContext
>;

const mockClearPhaseNotification = jest.fn();

const defaultMockContext = {
  fieldNotifications: {},
  setNotificationForField: jest.fn(),
  setNotificationForFields: jest.fn(),
  clearNotificationForField: jest.fn(),
  phaseNotification: {},
  setPhaseNotification: jest.fn(),
  clearPhaseNotification: mockClearPhaseNotification,
};

describe('PhaseAlert', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseNotificationContext.mockReturnValue(defaultMockContext);
  });

  it('should render nothing when no phase notifications exist', () => {
    const { container } = renderWithSimpleProviders(<PhaseAlert />);
    expect(container.firstChild).toBeNull();
  });

  it('should render success alert', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: [
          {
            id: 'success-1',
            title: 'Success Title',
            message: 'Success message content',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    expect(screen.getByText('Success Title')).toBeInTheDocument();
    expect(screen.getByText('Success message content')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
  });

  it('should render warning alert', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.WARNING]: [
          {
            id: 'warning-1',
            title: 'Warning Title',
            message: 'Warning message content',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    expect(screen.getByText('Warning Title')).toBeInTheDocument();
    expect(screen.getByText('Warning message content')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
  });

  it('should render error alert', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.ERROR]: [
          {
            id: 'error-1',
            title: 'Error Title',
            message: 'Error message content',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    expect(screen.getByText('Error Title')).toBeInTheDocument();
    expect(screen.getByText('Error message content')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /close/i })).toBeInTheDocument();
  });

  it('should render multiple alerts of the same level', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: [
          {
            id: 'success-1',
            title: 'First Success',
            message: 'First success message',
          },
          {
            id: 'success-2',
            title: 'Second Success',
            message: 'Second success message',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    expect(screen.getByText('First Success')).toBeInTheDocument();
    expect(screen.getByText('First success message')).toBeInTheDocument();
    expect(screen.getByText('Second Success')).toBeInTheDocument();
    expect(screen.getByText('Second success message')).toBeInTheDocument();
    expect(screen.getAllByRole('button', { name: /close/i })).toHaveLength(2);
  });

  it('should render multiple alerts of different levels', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: [
          {
            id: 'success-1',
            title: 'Success Title',
            message: 'Success message',
          },
        ],
        [NotificationLevel.WARNING]: [
          {
            id: 'warning-1',
            title: 'Warning Title',
            message: 'Warning message',
          },
        ],
        [NotificationLevel.ERROR]: [
          {
            id: 'error-1',
            title: 'Error Title',
            message: 'Error message',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    expect(screen.getByText('Success Title')).toBeInTheDocument();
    expect(screen.getByText('Warning Title')).toBeInTheDocument();
    expect(screen.getByText('Error Title')).toBeInTheDocument();
    expect(screen.getAllByRole('button', { name: /close/i })).toHaveLength(3);
  });

  it('should call clearPhaseNotification when success alert is closed', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: [
          {
            id: 'success-1',
            title: 'Success Title',
            message: 'Success message',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockClearPhaseNotification).toHaveBeenCalledWith(NotificationLevel.SUCCESS, 'success-1');
  });

  it('should call clearPhaseNotification when warning alert is closed', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.WARNING]: [
          {
            id: 'warning-1',
            title: 'Warning Title',
            message: 'Warning message',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockClearPhaseNotification).toHaveBeenCalledWith(NotificationLevel.WARNING, 'warning-1');
  });

  it('should call clearPhaseNotification when error alert is closed', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.ERROR]: [
          {
            id: 'error-1',
            title: 'Error Title',
            message: 'Error message',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    expect(mockClearPhaseNotification).toHaveBeenCalledWith(NotificationLevel.ERROR, 'error-1');
  });

  it('should handle empty notification arrays gracefully', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: [],
        [NotificationLevel.WARNING]: [],
        [NotificationLevel.ERROR]: [],
      },
    });

    const { container } = renderWithSimpleProviders(<PhaseAlert />);
    expect(container.firstChild).toBeNull();
  });

  it('should handle undefined notification arrays gracefully', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: undefined,
        [NotificationLevel.WARNING]: undefined,
        [NotificationLevel.ERROR]: undefined,
      },
    });

    const { container } = renderWithSimpleProviders(<PhaseAlert />);
    expect(container.firstChild).toBeNull();
  });

  it('should call clearPhaseNotification with correct parameters for multiple alerts', () => {
    mockUseNotificationContext.mockReturnValue({
      ...defaultMockContext,
      phaseNotification: {
        [NotificationLevel.SUCCESS]: [
          {
            id: 'success-1',
            title: 'First Success',
            message: 'First message',
          },
          {
            id: 'success-2',
            title: 'Second Success',
            message: 'Second message',
          },
        ],
      },
    });

    renderWithSimpleProviders(<PhaseAlert />);

    const closeButtons = screen.getAllByRole('button', { name: /close/i });
    
    // Click first close button
    fireEvent.click(closeButtons[0]);
    expect(mockClearPhaseNotification).toHaveBeenCalledWith(NotificationLevel.SUCCESS, 'success-1');

    // Click second close button
    fireEvent.click(closeButtons[1]);
    expect(mockClearPhaseNotification).toHaveBeenCalledWith(NotificationLevel.SUCCESS, 'success-2');

    expect(mockClearPhaseNotification).toHaveBeenCalledTimes(2);
  });
});
