import React, {useMemo} from 'react';
import {useIntl} from 'react-intl';

import {Box} from '@regrow-internal/design-system';

import type {Farm, Field} from '__generated__/gql/graphql';
import {SearchInput} from '_common/components/search-input';

import {useNotificationContext} from 'views/mrv/project/phases/stages/notifications/NotificationContext';
import type {NotificationLookup} from 'views/mrv/project/phases/stages/notifications/types';

import type {FarmFieldNavProps} from './FarmFieldNavigation';
import {MemoizedFieldNavItems} from './FieldNavItems';

export type FieldNavItemType = Field & {
  notifications: NotificationLookup | null;
  _searchName: string;
  _searchId: string;
};

export type FarmWithSearchFields = Omit<Farm, 'fields'> & {
  fields: Array<FieldNavItemType>;
};

export const FarmNavItems = ({
  farms,
  onFieldClick,
  selectedFieldId,
  fieldCompletionLookup,
}: FarmFieldNavProps & {farms: Array<Farm>}) => {
  const {fieldNotifications} = useNotificationContext();
  const [searchText, setSearchText] = React.useState('');
  const {formatMessage} = useIntl();

  const indexedFarms = useMemo<Array<FarmWithSearchFields>>(() => {
    return farms.map(farm => ({
      ...farm,
      fields: (farm.fields ?? []).map(field => ({
        ...field,
        notifications: fieldNotifications[field.id] || null,
        _searchName: field.name.toLowerCase(),
        _searchId: field.id.toLowerCase(),
      })),
    }));
  }, [farms, fieldNotifications]);

  const filteredFarms = useMemo<Array<FarmWithSearchFields>>(() => {
    if (!searchText) return indexedFarms;

    const term = searchText.toLowerCase();

    return indexedFarms?.reduce<Array<FarmWithSearchFields>>((acc, farm) => {
      const filteredFields = (farm.fields ?? []).filter(field => {
        return field._searchId.includes(term) || field._searchName.includes(term);
      });

      if (filteredFields.length > 0) {
        acc.push({
          ...farm,
          fields: filteredFields,
        });
      }

      return acc;
    }, []);
  }, [searchText, indexedFarms]);

  return (
    <>
      <Box height="100%" display="flex" flexDirection="column" gap={1} pl={1} py={1}>
        <SearchInput
          value={searchText}
          onChange={e => {
            setSearchText(e);
          }}
          minWidth={'100%'}
          placeholder={formatMessage({
            id: 'Search',
            defaultMessage: 'Search',
          })}
          hasClear
        />
      </Box>
      <Box
        sx={{
          position: 'absolute',
          overflowY: 'auto',
          top: 50,
          bottom: 0,
          left: 0,
          right: 0,
          paddingBottom: '60px' /* To match the gradient height from the parent sidebar column. */,
          scrollPaddingBottom: '60px',
          scrollbarWidth: 'thin',
        }}
      >
        {filteredFarms.map(farm => (
          <MemoizedFieldNavItems
            key={`${farm.name}-${farm.id}`}
            farm={farm}
            selectedFieldId={selectedFieldId}
            onFieldClick={onFieldClick}
            fieldCompletionLookup={fieldCompletionLookup}
            searchText={searchText}
          />
        ))}
      </Box>
    </>
  );
};
