import uniqueId from 'lodash/uniqueId';
import React, {createContext, useCallback, useContext, useEffect, useMemo} from 'react';
import type {FC} from 'react';

import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

import type {FieldNotifications, NotificationContent, NotificationLookup} from './types';
import {NotificationLevel} from './types';

type CreateNotificationContent = Omit<NotificationContent, 'id'>;

export type NotificationContextTypeType = {
  fieldNotifications: FieldNotifications;
  setNotificationForField: (
    fieldId: string,
    level: NotificationLevel,
    content: CreateNotificationContent
  ) => void;
  setNotificationForFields: (
    fieldIds: Array<string>,
    level: NotificationLevel,
    content: CreateNotificationContent
  ) => void;
  clearNotificationForField: (fieldId: string, level?: NotificationLevel) => void;
  phaseNotification: NotificationLookup;
  setPhaseNotification: (level: NotificationLevel, content: CreateNotificationContent) => void;
  clearPhaseNotification: (level?: NotificationLevel) => void;
};

const initialState = {
  fieldNotifications: {},
  setNotificationForField: () => undefined,
  setNotificationForFields: () => undefined,
  clearNotificationForField: () => undefined,
  phaseNotification: {},
  setPhaseNotification: () => undefined,
  clearPhaseNotification: () => undefined,
};

const NotificationContextType = createContext<NotificationContextTypeType>(initialState);

export const NotificationContextProvider: FC = ({children}) => {
  const {selectedField} = useSelectedFieldContext();
  const [fieldNotifications, _setNotificationForField] = React.useState<FieldNotifications>(
    initialState.fieldNotifications
  );
  const [phaseNotification, _setPhaseNotification] = React.useState<NotificationLookup>(
    initialState.phaseNotification
  );

  const setPhaseNotification = useCallback(
    (level: NotificationLevel, content: CreateNotificationContent) => {
      _setPhaseNotification(previousState => {
        const notificationsForLevel = previousState[level];
        const notificationContent = {
          ...content,
          id: uniqueId(),
        };

        return {
          ...previousState,
          [level]: notificationsForLevel
            ? [...notificationsForLevel, notificationContent]
            : [notificationContent],
        };
      });
    },
    [_setPhaseNotification]
  );

  const clearPhaseNotification = useCallback(
    (level?: NotificationLevel) => {
      _setPhaseNotification(previousState => {
        if (level) {
          return {
            ...previousState,
            [level]: undefined,
          };
        } else {
          return {};
        }
      });
    },
    [_setPhaseNotification]
  );

  const setNotificationForField = useCallback(
    (fieldId: string, level: NotificationLevel, content: CreateNotificationContent) => {
      _setNotificationForField(previousState => {
        const notificationsForField = previousState[fieldId];
        const notificationsForLevel = notificationsForField ? notificationsForField[level] : null;

        const notification = {
          ...content,
          id: uniqueId(),
        };

        return {
          ...previousState,
          [fieldId]: {
            ...notificationsForField,
            [level]:
              notificationsForField && notificationsForLevel
                ? [...notificationsForLevel, notification]
                : [notification],
          },
        };
      });
    },
    []
  );

  const setNotificationForFields = useCallback(
    (fieldIds: Array<string>, level: NotificationLevel, content: CreateNotificationContent) => {
      _setNotificationForField(previousState => {
        const newState = {...previousState};

        fieldIds.forEach(fieldId => {
          const notificationsForField = previousState[fieldId];
          const notificationsForLevel = notificationsForField ? notificationsForField[level] : null;
          const notification = {
            ...content,
            id: uniqueId(),
          };

          newState[fieldId] = {
            ...notificationsForField,
            [level]:
              notificationsForField && notificationsForLevel
                ? [...notificationsForLevel, notification]
                : [notification],
          };
        });

        return newState;
      });
    },
    []
  );

  const clearNotificationForField = useCallback((fieldId: string, level?: NotificationLevel) => {
    _setNotificationForField(previousState => {
      const previousFieldState = previousState[fieldId];

      if (!previousFieldState) {
        return previousState;
      }

      if (level) {
        return {
          ...previousState,
          [fieldId]: {
            ...previousFieldState,
            [level]: undefined,
          },
        };
      } else {
        const newState = {...previousState};
        delete newState[fieldId];
        return newState;
      }
    });
  }, []);

  useEffect(() => {
    const selectedFieldId = selectedField?.id;
    if (selectedFieldId && fieldNotifications[selectedFieldId]?.[NotificationLevel.SUCCESS]) {
      clearNotificationForField(selectedFieldId, NotificationLevel.SUCCESS);
    }
  }, [selectedField, fieldNotifications, clearNotificationForField]);

  const selectedFieldContext = useMemo(
    () => ({
      fieldNotifications,
      setNotificationForField,
      setNotificationForFields,
      clearNotificationForField,
      phaseNotification,
      setPhaseNotification,
      clearPhaseNotification,
    }),
    [
      fieldNotifications,
      clearNotificationForField,
      setNotificationForField,
      setNotificationForFields,
      phaseNotification,
      setPhaseNotification,
      clearPhaseNotification,
    ]
  );

  return (
    <NotificationContextType.Provider value={selectedFieldContext}>
      {children}
    </NotificationContextType.Provider>
  );
};

export const useNotificationContext = () => {
  return useContext<NotificationContextTypeType>(NotificationContextType);
};
