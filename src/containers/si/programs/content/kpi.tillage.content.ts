import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
  CROPLAND_SATELLITE_VERIFIED_TEXT,
} from 'containers/si/constants';
import {getUnit} from 'containers/si/utils/convert';

export const TILLAGE_CONTENT = {
  name: 'conservation tillage',
  explainer: (panelLabel: string) => (userUnitsSystem: MeasurementEnum) => [
    `The percentage of cropland practicing reduced tillage or no tillage broken down ${panelLabel}.`,
    `The percentage adoption is based on on ${CROPLAND_SATELLITE_VERIFIED_TEXT} ${
      getUnit(userUnitsSystem).unitName.plural
    } only (${
      getUnit(userUnitsSystem).unitName.plural
    } that we can confidently determine tillage practices on fields through remote-sensing). Adoption is not determined on ${CROPLAND_SATELLITE_UNVERIFIED_TEXT} ${
      getUnit(userUnitsSystem).unitName.plural
    }.`,
  ],
  categoryColor: '1' as const,
};
