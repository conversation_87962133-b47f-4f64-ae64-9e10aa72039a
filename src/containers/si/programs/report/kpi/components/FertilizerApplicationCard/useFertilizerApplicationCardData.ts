import pick from 'lodash/pick';
import uniq from 'lodash/uniq';
import {useMemo} from 'react';

import {useAppSelector} from 'store/useRedux';

import {getTypedValues} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {selectMeasurement} from 'containers/login/login-selectors';
import {useFetchAndTransformKPI} from 'containers/si/hooks/useFetchAndTransformKPI';
import {FERT_PER_AREA_METRICS_LABEL_MAP} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fertilizer_per_area.transformation';
import {
  CATEGORY_COLOR_KEY,
  FORMATTER_MAP,
  KPI,
  METRIC_MAP,
  PANEL_CONTENT,
  TRANSFORMER,
  UNIT_MAP,
} from 'containers/si/programs/report/kpi/components/FertilizerApplicationCard/content';
import {type PrimaryMetricTileProps} from 'containers/si/programs/report/kpi/components/PrimaryMetricTile';
import {type TimeTrendTileProps} from 'containers/si/programs/report/kpi/components/TimeTrendTile';
import {useCropSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useCropSummaryTypeBarChartContent';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {usePrimaryMetricTileAndTimeTrendTileContent} from 'containers/si/programs/report/kpi/hooks/usePrimaryMetricTileAndTimeTrendTileContent';
import {useSubregionSummaryTypeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubregionSummaryTypeBarChartContent';
import {useSubTypeStackedOverTimeBarChartContent} from 'containers/si/programs/report/kpi/hooks/useSubTypeStackedOverTimeBarChartContent';
import type {
  SubTypeStackedOverTimeBarChartProps,
  SummaryTypeChartProps,
} from 'containers/si/programs/report/kpi/types';

export type UseFertilizerApplicationCardData = {
  isLoading: boolean;
  data: {
    primaryMetricTileContent: PrimaryMetricTileProps | null;
    timeTrendTileContent: TimeTrendTileProps | null;
    nutrientTypeOverTimeBarChartContent: SubTypeStackedOverTimeBarChartProps | null;
    cropSummaryTypeBarChartContent: SummaryTypeChartProps | null;
    subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null;
  };
};

const BY_SUBTYPE_LABEL_MAP = pick(FERT_PER_AREA_METRICS_LABEL_MAP, [
  'fertNPerArea',
  'fertPPerArea',
  'fertKPerArea',
  'fertSPerArea',
]);

export const useFertilizerApplicationCardData = (): UseFertilizerApplicationCardData => {
  const {filtersState, commonFilters} = useGetKPIDashboardCommonFilters();

  const userUnitsSystem = useAppSelector(selectMeasurement);
  const kpiTransformer = useMemo(() => TRANSFORMER(userUnitsSystem), [userUnitsSystem]);

  const summariesForFetch = useMemo(
    () =>
      uniq(
        getTypedValues(PANEL_CONTENT)
          .map(({isDisabledMessage, fetchSummary}) =>
            !isDisabledMessage(filtersState) ? fetchSummary : undefined
          )
          .filter(isDefined)
      ),
    [filtersState]
  );

  const {
    isLoading,
    annualizedSummary: byYearMetricsPairs,
    boundarySummary: bySubregionMetricsPairs,
    cropTypeSummary: byCropMetricsPairs,
  } = useFetchAndTransformKPI({
    kpi: KPI,
    summaries: summariesForFetch,
    commonFilters,
    kpiTransformer,
  });

  const {primaryMetricTileContent, timeTrendTileContent} =
    usePrimaryMetricTileAndTimeTrendTileContent({
      byYearMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
      isDownwardChangePositive: PANEL_CONTENT.ByYear.isDownwardChangePositive,
    });

  // all metrics (defined by labelMap keys) must have the same unitDetail and formatter
  const nutrientTypeOverTimeBarChartContent = useSubTypeStackedOverTimeBarChartContent({
    byYearMetricsPairs,
    filtersState,
    formatter: FORMATTER_MAP['fertMassPerArea'],
    labelMap: BY_SUBTYPE_LABEL_MAP,
    unitDetail: UNIT_MAP['fertMassPerArea'](userUnitsSystem),
  });

  const cropSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useCropSummaryTypeBarChartContent({
      byCropMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      filtersState,
      isLoading,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const subregionSummaryTypeBarChartContent: SummaryTypeChartProps | null =
    useSubregionSummaryTypeBarChartContent({
      bySubregionMetricsPairs,
      categoryColorKey: CATEGORY_COLOR_KEY,
      isLoading,
      filtersState,
      formatterMap: FORMATTER_MAP,
      metricMap: METRIC_MAP,
      unitMap: UNIT_MAP,
    });

  const data = {
    subregionSummaryTypeBarChartContent,
    cropSummaryTypeBarChartContent,
    nutrientTypeOverTimeBarChartContent,
    primaryMetricTileContent,
    timeTrendTileContent,
  };

  return {isLoading, data};
};
