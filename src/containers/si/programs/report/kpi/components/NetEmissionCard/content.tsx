import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {NET_IMPACT_PER_AREA_CONTENT} from 'containers/si/programs/content/kpi.net_impact_kg_per_m2.content';
import type {MetricLookupMap} from 'containers/si/programs/explore/types';
import {
  makeNetImpactMetrics,
  NET_IMPACT_METRICS_FORMATTER_MAP,
  NET_IMPACT_METRICS_UNIT_MAP,
  type NetImpactMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.net_impact_kg_per_m2.transformation';
import {PANEL_LABEL} from 'containers/si/programs/report/kpi/constants';

export const KPI = 'net_impact_kg_per_m2';
export const FORMATTER_MAP = NET_IMPACT_METRICS_FORMATTER_MAP;
export const UNIT_MAP = NET_IMPACT_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<
  MetricLookupMap<NetImpactMetrics>,
  'primaryMetricKey' | 'uncertaintyMetricKey'
> = {
  primaryMetricKey: 'netImpact',
  uncertaintyMetricKey: 'netImpactStdErr',
};
export const TRANSFORMER = makeNetImpactMetrics;
export const CATEGORY_COLOR_KEY = NET_IMPACT_PER_AREA_CONTENT.categoryColor;

export const CARD_CONTENT = {
  title: NET_IMPACT_PER_AREA_CONTENT.name,
  isDownwardChangePositive: true,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: NET_IMPACT_PER_AREA_CONTENT.explainer(
      PANEL_LABEL.OVER_TIME,
      'specified subregion(s)'
    ),
  },
  ByCrop: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: NET_IMPACT_PER_AREA_CONTENT.explainer(
      PANEL_LABEL.BY_CROP_TYPE,
      'specified subregion(s) growing the given commodity'
    ),
  },
  ByBoundary: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: NET_IMPACT_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_AREA, 'given subregion'),
  },
} as const;
