import { screen } from '@testing-library/react';
import React from 'react';

import { renderWithSimpleProviders } from '_common/test_utils/renderWithProviders';
import { PhaseTypes } from '__generated__/mrv/mrvApi.types';

import { FieldEventsStageWrapper } from '../FieldEventsStageWrapper';
import { useNotificationContext } from '../../notifications/NotificationContext';
import { NotificationLevel } from '../../notifications/types';

// Mock the notification context
jest.mock('../../notifications/NotificationContext', () => ({
  ...jest.requireActual('../../notifications/NotificationContext'),
  useNotificationContext: jest.fn(),
}));

// Mock the FieldEventContext
jest.mock('../FieldEventContext', () => ({
  FieldEventContextProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="field-event-context-provider">{children}</div>
  ),
}));

// Mock the alert components
jest.mock('../../notifications/alerts/MonitorPrefillAlert', () => ({
  MonitorPrefillAlert: () => <div data-testid="monitor-prefill-alert">Monitor Prefill Alert</div>,
}));

jest.mock('../../notifications/alerts/IntendedPracticesPrefillAlert', () => ({
  IntendedPracticesPrefillAlert: () => (
    <div data-testid="intended-practices-prefill-alert">Intended Practices Prefill Alert</div>
  ),
}));

jest.mock('../../notifications/alerts/PhaseAlert', () => ({
  PhaseAlert: () => <div data-testid="phase-alert">Phase Alert</div>,
}));

jest.mock('../field-event-table/table-components/FieldEventsTableWrapper', () => ({
  FieldEventsTableWrapper: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="field-events-table-wrapper">{children}</div>
  ),
}));

const mockUseNotificationContext = useNotificationContext as jest.MockedFunction<
  typeof useNotificationContext
>;

const defaultMockContext = {
  fieldNotifications: {},
  setNotificationForField: jest.fn(),
  setNotificationForFields: jest.fn(),
  clearNotificationForField: jest.fn(),
  phaseNotification: {},
  setPhaseNotification: jest.fn(),
  clearPhaseNotification: jest.fn(),
};

const defaultProps = {
  title: 'Test Stage Title',
  description: <span>Test stage description</span>,
  children: <div data-testid="stage-children">Stage Content</div>,
  currentPhase: undefined,
};

describe('FieldEventsStageWrapper', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseNotificationContext.mockReturnValue(defaultMockContext);
  });

  it('should render title and description', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    expect(screen.getByText('Test Stage Title')).toBeInTheDocument();
    expect(screen.getByText('Test stage description')).toBeInTheDocument();
  });

  it('should render children content', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    expect(screen.getByTestId('stage-children')).toBeInTheDocument();
    expect(screen.getByText('Stage Content')).toBeInTheDocument();
  });

  it('should always render IntendedPracticesPrefillAlert', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    expect(screen.getByTestId('intended-practices-prefill-alert')).toBeInTheDocument();
  });

  it('should always render PhaseAlert', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    expect(screen.getByTestId('phase-alert')).toBeInTheDocument();
  });

  it('should always render FieldEventsTableWrapper', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    expect(screen.getByTestId('field-events-table-wrapper')).toBeInTheDocument();
  });

  it('should render MonitorPrefillAlert when currentPhase is ENROLMENT', () => {
    renderWithSimpleProviders(
      <FieldEventsStageWrapper {...defaultProps} currentPhase={PhaseTypes.ENROLMENT} />
    );

    expect(screen.getByTestId('monitor-prefill-alert')).toBeInTheDocument();
  });

  it('should not render MonitorPrefillAlert when currentPhase is not ENROLMENT', () => {
    renderWithSimpleProviders(
      <FieldEventsStageWrapper {...defaultProps} currentPhase={PhaseTypes.MONITORING} />
    );

    expect(screen.queryByTestId('monitor-prefill-alert')).not.toBeInTheDocument();
  });

  it('should not render MonitorPrefillAlert when currentPhase is undefined', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} currentPhase={undefined} />);

    expect(screen.queryByTestId('monitor-prefill-alert')).not.toBeInTheDocument();
  });

  it('should wrap IntendedPracticesPrefillAlert with FieldEventContextProvider', () => {
    renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    const contextProvider = screen.getByTestId('field-event-context-provider');
    const intendedPracticesAlert = screen.getByTestId('intended-practices-prefill-alert');

    expect(contextProvider).toBeInTheDocument();
    expect(contextProvider).toContainElement(intendedPracticesAlert);
  });

  it('should render all components in correct order', () => {
    const { container } = renderWithSimpleProviders(<FieldEventsStageWrapper {...defaultProps} />);

    const elements = container.querySelectorAll('[data-testid]');
    const testIds = Array.from(elements).map(el => el.getAttribute('data-testid'));

    expect(testIds).toEqual([
      'field-event-context-provider',
      'intended-practices-prefill-alert',
      'phase-alert',
      'field-events-table-wrapper',
      'stage-children',
    ]);
  });

  it('should render MonitorPrefillAlert in correct position when phase is ENROLMENT', () => {
    const { container } = renderWithSimpleProviders(
      <FieldEventsStageWrapper {...defaultProps} currentPhase={PhaseTypes.ENROLMENT} />
    );

    const elements = container.querySelectorAll('[data-testid]');
    const testIds = Array.from(elements).map(el => el.getAttribute('data-testid'));

    expect(testIds).toEqual([
      'monitor-prefill-alert',
      'field-event-context-provider',
      'intended-practices-prefill-alert',
      'phase-alert',
      'field-events-table-wrapper',
      'stage-children',
    ]);
  });

  it('should handle complex description content', () => {
    const complexDescription = (
      <div>
        <p>First paragraph</p>
        <p>Second paragraph with <strong>bold text</strong></p>
      </div>
    );

    renderWithSimpleProviders(
      <FieldEventsStageWrapper {...defaultProps} description={complexDescription} />
    );

    expect(screen.getByText('First paragraph')).toBeInTheDocument();
    expect(screen.getByText('Second paragraph with')).toBeInTheDocument();
    expect(screen.getByText('bold text')).toBeInTheDocument();
  });

  it('should handle complex children content', () => {
    const complexChildren = (
      <div>
        <button>Action Button</button>
        <table>
          <tbody>
            <tr>
              <td>Table Cell</td>
            </tr>
          </tbody>
        </table>
      </div>
    );

    renderWithSimpleProviders(
      <FieldEventsStageWrapper {...defaultProps} children={complexChildren} />
    );

    expect(screen.getByRole('button', { name: 'Action Button' })).toBeInTheDocument();
    expect(screen.getByText('Table Cell')).toBeInTheDocument();
  });

  it('should handle all phase types correctly', () => {
    const phaseTypes = [
      PhaseTypes.ENROLMENT,
      PhaseTypes.MONITORING,
      PhaseTypes.VERIFICATION,
      PhaseTypes.ISSUANCE,
    ];

    phaseTypes.forEach(phaseType => {
      const { unmount } = renderWithSimpleProviders(
        <FieldEventsStageWrapper {...defaultProps} currentPhase={phaseType} />
      );

      if (phaseType === PhaseTypes.ENROLMENT) {
        expect(screen.getByTestId('monitor-prefill-alert')).toBeInTheDocument();
      } else {
        expect(screen.queryByTestId('monitor-prefill-alert')).not.toBeInTheDocument();
      }

      // Always present components
      expect(screen.getByTestId('intended-practices-prefill-alert')).toBeInTheDocument();
      expect(screen.getByTestId('phase-alert')).toBeInTheDocument();
      expect(screen.getByTestId('field-events-table-wrapper')).toBeInTheDocument();

      unmount();
    });
  });
});
