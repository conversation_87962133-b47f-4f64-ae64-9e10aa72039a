import React, {useMemo} from 'react';

import {CardTooltip} from 'containers/si/programs/report/kpi/components/CardTooltip';
import {OverTimePanel} from 'containers/si/programs/report/kpi/components/OverTimePanel';
import {PANEL_CONTENT} from 'containers/si/programs/report/kpi/components/VolumeWeightedSocEFCard/content';
import {type UseVolumeWeightedSocEFCardData} from 'containers/si/programs/report/kpi/components/VolumeWeightedSocEFCard/useVolumeWeightedSocEFCardData';
import {useGetKPIDashboardCommonFilters} from 'containers/si/programs/report/kpi/hooks/useGetKPIDashboardCommonFilters';
import {useSortedPaginatedSummaryTypeBarChart} from 'containers/si/programs/report/kpi/hooks/useSortedPaginatedSummaryTypeBarChart';

export const useVolumeWeightedSocEFCardPanels = ({
  isLoading,
  data: {
    primaryMetricTileContent,
    timeTrendTileContent,
    cropSummaryTypeBarChartContent,
    hasPurchaseVolume,
  },
}: UseVolumeWeightedSocEFCardData) => {
  const {filtersState} = useGetKPIDashboardCommonFilters();
  const OverTimePanelComponent = useMemo(
    () => (
      <OverTimePanel
        primaryMetricTileContent={primaryMetricTileContent}
        timeTrendTileContent={timeTrendTileContent}
      />
    ),
    [primaryMetricTileContent, timeTrendTileContent]
  );

  const {
    PaginationComponent: ByCropPagination,
    SortComponent: ByCropSort,
    ChartComponent: ByCropChart,
  } = useSortedPaginatedSummaryTypeBarChart({
    isLoading,
    defaultSort: {by: 'VALUE', direction: 'DESC', valueSortDirection: 'DESC'},
    summaryTypeBarChartProps: cropSummaryTypeBarChartContent,
  });

  const panelsLookup = useMemo(
    () => ({
      [PANEL_CONTENT.ByYear.label]: {
        component: OverTimePanelComponent,
        ...PANEL_CONTENT.ByYear,
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByYear.title}
            body={PANEL_CONTENT.ByYear.tooltipContent}
          />
        ),
        isDisabledMessage: PANEL_CONTENT.ByYear.isDisabledMessage(filtersState, hasPurchaseVolume),
      },
      [PANEL_CONTENT.ByCrop.label]: {
        component: ByCropChart,
        pagination: ByCropPagination,
        menuItems: ByCropSort,
        ...PANEL_CONTENT.ByCrop,
        tooltip: (
          <CardTooltip
            title={PANEL_CONTENT.ByCrop.title}
            body={PANEL_CONTENT.ByCrop.tooltipContent}
          />
        ),
        isDisabledMessage: PANEL_CONTENT.ByCrop.isDisabledMessage(filtersState, hasPurchaseVolume),
      },
    }),
    [
      OverTimePanelComponent,
      filtersState,
      hasPurchaseVolume,
      ByCropChart,
      ByCropPagination,
      ByCropSort,
    ]
  );

  return panelsLookup;
};
