import type {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {capitalizeFirstLetter} from '_common/utils/string';

import {FERTILIZER_PER_AREA_CONTENT} from 'containers/si/programs/content/kpi.fertilizer_per_area.content';
import {
  FERT_PER_AREA_METRICS_FORMATTER_MAP,
  FERT_PER_AREA_METRICS_UNIT_MAP,
  makeFertPerAreaMetrics,
  type FertPerAreaMetrics,
} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.fertilizer_per_area.transformation';
import {
  NO_DATA_FOR_MULTIPLE_CROPS_COPY,
  PANEL_LABEL,
} from 'containers/si/programs/report/kpi/constants';
import type {MetricLookupMap} from 'containers/si/programs/report/kpi/types';
import type {KPIDataFiltersState} from 'containers/si/types';
import {getUnit} from 'containers/si/utils/convert';

export const KPI = 'fertilizer_per_area';
export const FORMATTER_MAP = FERT_PER_AREA_METRICS_FORMATTER_MAP;
export const UNIT_MAP = FERT_PER_AREA_METRICS_UNIT_MAP;
export const METRIC_MAP: Pick<MetricLookupMap<FertPerAreaMetrics>, 'primaryMetricKey'> = {
  primaryMetricKey: 'fertMassPerArea',
};
export const TRANSFORMER = makeFertPerAreaMetrics;
export const CATEGORY_COLOR_KEY = FERTILIZER_PER_AREA_CONTENT.categoryColor;

const PANEL_LABEL_BY_NUTRIENT_TYPE = 'by nutrient type';

const CARD_CONTENT = {
  title: (userUnitsSystem: MeasurementEnum) =>
    `${capitalizeFirstLetter(FERTILIZER_PER_AREA_CONTENT.name)} per ${
      getUnit(userUnitsSystem).unitName.singular
    }`,
};

export const PANEL_CONTENT = {
  ByYear: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.OVER_TIME}`,
    label: PANEL_LABEL.OVER_TIME,
    tooltipContent: FERTILIZER_PER_AREA_CONTENT.explainer(PANEL_LABEL.OVER_TIME),
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
    isDownwardChangePositive: true,
  },
  ByCrop: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_CROP_TYPE}`,
    label: PANEL_LABEL.BY_CROP_TYPE,
    tooltipContent: FERTILIZER_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_CROP_TYPE),
    isDisabledMessage: (_filters: KPIDataFiltersState) => undefined,
    fetchSummary: 'crop_type',
  },
  ByBoundary: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL.BY_AREA}`,
    label: PANEL_LABEL.BY_AREA,
    tooltipContent: FERTILIZER_PER_AREA_CONTENT.explainer(PANEL_LABEL.BY_AREA),
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'boundary',
  },
  ByType: {
    title: (userUnitsSystem: MeasurementEnum) =>
      `${CARD_CONTENT.title(userUnitsSystem)} ${PANEL_LABEL_BY_NUTRIENT_TYPE}`,
    label: PANEL_LABEL_BY_NUTRIENT_TYPE,
    tooltipContent: FERTILIZER_PER_AREA_CONTENT.explainer(PANEL_LABEL_BY_NUTRIENT_TYPE),
    isDisabledMessage: (filters: KPIDataFiltersState) =>
      filters.cropIds.length !== 1 ? NO_DATA_FOR_MULTIPLE_CROPS_COPY : undefined,
    fetchSummary: 'annualized',
  },
} as const;
