import {type Theme} from '@regrow-internal/design-system';

import {type MeasurementEnum} from '_common/utils/measurement-unit-options';

import {
  CROPLAND_NOTAPPLICABLE_TEXT,
  CROPLAND_SATELLITE_UNVERIFIED_TEXT,
} from 'containers/si/constants';
import {type GreennessLevelMetrics} from 'containers/si/programs/helpers/kpiResponseTransformers/kpi.greenness_level_area_m2.transformation';
import {getUnit} from 'containers/si/utils/convert';

export const GREENNESS_LEVEL_CONTENT = {
  name: 'Off-season green cover',
  explainer: (userUnitsSystem: MeasurementEnum) => [
    `An estimate of ${
      getUnit(userUnitsSystem).unitName.plural
    } covered by green vegetation during the non-commodity (off-season) period over time. The timeframe considered off-season is different for each commodity.`,
    'The extent of green cover can be driven by multiple sources including cover crops, weeds, volunteer crops, etc.',
    `The ${getUnit(userUnitsSystem).unitName.plural} of green cover is based on verified ${
      getUnit(userUnitsSystem).unitName.plural
    } only. Green cover is not assessed on unverified ${
      getUnit(userUnitsSystem).unitName.plural
    } (${
      getUnit(userUnitsSystem).unitName.plural
    } that we cannot confidently determine green cover on through remote-sensing) or not applicable ${
      getUnit(userUnitsSystem).unitName.plural
    } (${
      getUnit(userUnitsSystem).unitName.plural
    } where green cover is classified as perennials, growth of weeds, or volunteer crops).`,
  ],
  categoryColor: '1' as const,
};
export const GREENNESSLEVEL_LABEL_MAP: Record<
  keyof GreennessLevelMetrics,
  {label: string; categoryPaletteColorKey: keyof Theme['palette']['categoryPalette']}
> = {
  bareArea: {label: 'no green cover', categoryPaletteColorKey: '6'},
  lowArea: {label: 'low green cover', categoryPaletteColorKey: '7'},
  moderateArea: {label: 'medium green cover', categoryPaletteColorKey: '8'},
  highArea: {label: 'high green cover', categoryPaletteColorKey: '1'},
  unknownArea: {label: CROPLAND_SATELLITE_UNVERIFIED_TEXT, categoryPaletteColorKey: '4'},
  notApplicableArea: {label: CROPLAND_NOTAPPLICABLE_TEXT, categoryPaletteColorKey: '3'},
};
