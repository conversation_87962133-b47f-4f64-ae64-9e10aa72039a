import type {MeasurementEnum} from '_common/utils/measurement-unit-options';

import {CH4, N2O} from 'containers/si/constants';
import {getUnit} from 'containers/si/utils/convert';

export const GHG_PER_AREA_CONTENT = {
  name: (userUnitsSystem: MeasurementEnum) =>
    `Field GHG emissions per ${getUnit(userUnitsSystem).unitName.singular}`,
  explainer:
    (panelLabel: string, denominatorString: string) => (userUnitsSystem: MeasurementEnum) => [
      `The average amount of field-based ${N2O} and ${CH4} emissions produced per ${
        getUnit(userUnitsSystem).unitName.singular
      } broken down ${panelLabel}. The denominator is the total cropland ${
        getUnit(userUnitsSystem).unitName.plural
      } of the ${denominatorString}.`,
      `Note that standard error can only be calculated by commodity and will only be provided when a single commodity is selected.`,
    ],
  categoryColor: '2' as const,
};
