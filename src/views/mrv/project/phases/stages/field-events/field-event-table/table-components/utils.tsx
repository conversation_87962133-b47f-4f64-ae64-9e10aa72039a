import capitalize from 'lodash/capitalize';
import uniqueId from 'lodash/uniqueId';
import type {MutableRefObject} from 'react';
import React from 'react';

import {
  Box,
  Checkbox,
  CircularProgress,
  Lab,
  SvgIcon,
  Tooltip,
  TypographyOverflow,
} from '@regrow-internal/design-system';

import {type FieldEventValues} from '__generated__/gql/graphql';
import {type StageTypes} from '__generated__/mrv/mrvApi.types';
import {isDefined, isEmptyObject, isString, isTruthy} from '_common/utils/typeGuards';
import type {FormatMessage} from '_translations/types';

import {inputHasValue} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-inputs/utils';
import type {FieldCultivationCycle} from 'views/mrv/project/phases/stages/field-events/FieldEventContext';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import type {UpdateNoPracticeObservation} from 'views/mrv/project/phases/stages/field-events/useFieldEventActions';
import {convertStringToDate} from 'views/mrv/project/phases/stages/field-events/utils/dateUtils';
import {
  canSaveFieldEvent,
  getEventTypeFromStageType,
  isNPOSet,
} from 'views/mrv/project/phases/stages/field-events/utils/fieldEventUtils';

import {CultivationCyclesPinningButton} from './CCPinningButton';
import {FieldEventTableHeaderTooltips} from './constants';
import {CultivationCycleCell} from './CultivationCycleCell';
import {DeleteFieldEventButton} from './FieldEventButtons';
import {NoPracticeObservationCell} from './NoPracticeObservationCell';
import type {
  BaseColumnProps,
  FieldEventGridColDef,
  FieldEventRowModel,
  PreProcessEditCellPropsProps,
} from './types';
import {NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE, NoPracticeObservationType} from './types';

export const rowHasValidationErrors = (values: Lab.GridEditRowProps): boolean => {
  return Object.entries(values).some(([_, value]) => {
    return value.error !== false && value.error !== null;
  });
};

export const isPrefilledRow = (eventValues: FieldEventValues) => {
  if (
    eventValues.__typename === FieldEventType.CroppingEvent &&
    !inputHasValue(eventValues.planting_date) &&
    !inputHasValue(eventValues.harvest_date)
  ) {
    return true;
  }

  if (
    eventValues.__typename === FieldEventType.FallowPeriod &&
    !inputHasValue(eventValues.start_date) &&
    !inputHasValue(eventValues.end_date)
  ) {
    return true;
  }

  if (
    eventValues.__typename === FieldEventType.ApplicationEvent &&
    !inputHasValue(eventValues.application_date)
  ) {
    return true;
  }

  if (
    eventValues.__typename === FieldEventType.TillageEvent &&
    !inputHasValue(eventValues.tillage_date)
  ) {
    return true;
  }

  if (
    eventValues.__typename === FieldEventType.IrrigationEvent &&
    !inputHasValue(eventValues.start_date) &&
    !inputHasValue(eventValues.end_date)
  ) {
    return true;
  }

  return false;
};

export const getRows = <T = FieldEventValues,>(
  cultivationCycles?: Array<FieldCultivationCycle> | null,
  programIsReadOnly?: boolean | null
): Array<FieldEventRowModel<T>> => {
  if (!cultivationCycles) return [];

  return cultivationCycles.flatMap((cultivationCycle): Array<FieldEventRowModel<T>> => {
    const cultCycleWithoutEvents = {
      ...cultivationCycle,
      events: undefined,
    };
    if (isTruthy(cultivationCycle?.events?.length) && cultivationCycle?.events?.length) {
      return cultivationCycle.events.map((event, i): FieldEventRowModel<T> => {
        const isPrefill = isPrefilledRow(event.event_values);

        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        return {
          id: isPrefill ? uniqueId() : event.id,
          ...event.event_values,
          meta: {
            type: event.type,
            cultivationCycle: cultCycleWithoutEvents,
            showCultivationCycle: i === 0,
            eventCount: cultivationCycle.events?.length || 0,
            isLocked: event.is_locked,
            isReadOnly: programIsReadOnly,
            isPrefill: isPrefill,
            isUnsavedEvent: isPrefill,
          },
        } as FieldEventRowModel<T>;
      });
    }
    return [
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      {
        id: cultivationCycle.id,
        meta: {
          type: 'CultivationCycle',
          cultivationCycle: cultCycleWithoutEvents,
          showCultivationCycle: true,
          eventCount: 0,
          isReadOnly: programIsReadOnly,
          isPrefill: false,
        },
      } as FieldEventRowModel<T>,
    ];
  });
};

const handleValidation =
  (attributeType: string, validator: BaseColumnProps['validator'], formatMessage: FormatMessage) =>
  ({row, props, otherFieldsProps}: PreProcessEditCellPropsProps) => {
    const rowWithUpdatedValue = {
      ...row,
      [attributeType]: props.value,
    };

    const rowToValidate = otherFieldsProps
      ? Object.entries(otherFieldsProps).reduce((a, [key, value]) => {
          return {...a, [key]: value?.value};
        }, rowWithUpdatedValue)
      : rowWithUpdatedValue;

    return {
      ...props,
      error:
        inputHasValue(props.value) && validator ? validator(rowToValidate, formatMessage) : null,
    };
  };

export const getColumn = <T extends FieldEventValues = FieldEventValues>({
  attributeType,
  stageType,
  formatMessage,
  validator,
}: BaseColumnProps): FieldEventGridColDef<T> => {
  return {
    field: attributeType,
    headerName: formatMessage({
      id: `stage.${stageType}.form.headers.${attributeType}`,
      defaultMessage: capitalize(attributeType.split('_').join(' ')),
    }),
    flex: 1,
    editable: true,
    sortable: false,
    disableColumnMenu: true,
    renderHeader: ({field, colDef}) => {
      const eventType = getEventTypeFromStageType(stageType);
      const toolTip = eventType ? getColumnHeaderTooltip(eventType, field) : null;

      const values = toolTip?.values;

      return (
        <Box
          display={'flex'}
          justifyContent={'space-between'}
          alignContent={'center'}
          width={'100%'}
        >
          <TypographyOverflow fontWeight="bold">{colDef.headerName}</TypographyOverflow>
          {toolTip && (
            <Tooltip
              title={toolTip ? formatMessage(toolTip, values) : ''}
              placement="top"
              slotProps={{
                tooltip: {
                  sx: {
                    a: {
                      color: theme => theme.palette.semanticPalette.textInverted.main,
                      textDecoration: 'underline',
                    },
                  },
                },
                popper: {
                  modifiers: [
                    {
                      name: 'offset',
                      options: {
                        offset: [0, -18],
                      },
                    },
                  ],
                },
              }}
            >
              <Box display="flex" alignItems="center" ml={2}>
                <SvgIcon type="info-circled" color="secondary" fontSize="h5" />
              </Box>
            </Tooltip>
          )}
        </Box>
      );
    },
    preProcessEditCellProps: validator
      ? handleValidation(attributeType, validator, formatMessage)
      : undefined,
  };
};

export const getDefaultRowEditState = (
  row: FieldEventRowModel,
  eventType: FieldEventType,
  canBypassEventLock: boolean = false
) => {
  const {isFieldPracticeCultivationCycle, editState} = getFieldPracticeRowEditState(
    row.meta,
    eventType
  );
  if (isFieldPracticeCultivationCycle && editState) {
    return {
      mode: editState,
      ignoreModifications: true,
    };
  } else {
    const shouldSetEditState = shouldRowBeInEditMode(row.meta, eventType, canBypassEventLock);
    if (shouldSetEditState) {
      return {mode: Lab.GridRowModes.Edit, ignoreModifications: true};
    }
  }

  return null;
};

export const getFieldPracticeRowEditState = (
  meta: FieldEventRowModel['meta'],
  eventType: FieldEventType
) => {
  const noPracticeObservationKey = NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE[eventType];
  const noPracticeObservationSet = isNPOSet(meta, noPracticeObservationKey);

  const isFieldPracticeCultivationCycle = !!(
    noPracticeObservationSet !== null && noPracticeObservationKey
  );
  const noPracticeObservationToggledOff =
    isFieldPracticeCultivationCycle && noPracticeObservationSet;

  const isNoCropCultCycle = !meta.cultivationCycle?.crop_type || !meta.showCultivationCycle;
  const isEmptyCCRow = !isNoCropCultCycle && meta.eventCount === 0;
  const shouldNotToggleState = (isNoCropCultCycle || meta.eventCount > 0) && !meta.isUnsavedEvent;
  const shouldBeInEditState =
    !noPracticeObservationToggledOff && (isEmptyCCRow || isTruthy(meta.isUnsavedEvent));

  return {
    isFieldPracticeCultivationCycle,
    editState: shouldNotToggleState
      ? null
      : shouldBeInEditState
      ? Lab.GridRowModes.Edit
      : Lab.GridRowModes.View,
  };
};

export const shouldRowBeInEditMode = (
  meta: FieldEventRowModel['meta'],
  eventType: FieldEventType,
  canBypassEventLock: boolean
): boolean => {
  if (meta.isReadOnly && !canBypassEventLock) {
    return false;
  }

  if (meta.isUnsavedEvent) {
    return true;
  }

  if (meta.isPrefill) {
    return true;
  }

  return !meta?.cultivationCycle?.crop_type && meta.eventCount === 0;
};

export const getNoPracticeObservationHeader = (practiceType: NoPracticeObservationType) => {
  switch (practiceType) {
    case NoPracticeObservationType.TillageEvent:
      return 'Tilled?';
    case NoPracticeObservationType.IrrigationEvent:
      return 'Irrigated?';
    case NoPracticeObservationType.ApplicationEvent:
      return 'Nutrients applied?';
    default:
      return '';
  }
};

export const getDefaultFieldEventsGridColumns = <T = FieldEventValues,>(
  apiRef: MutableRefObject<Lab.GridApiPro>,
  columns: Array<FieldEventGridColDef<T>>,
  eventType: FieldEventType | null,
  stageType: string | null | undefined,
  handleToggleRowEditMode: (id: FieldEventRowModel['id'], toggleOn: boolean) => () => void,
  handleCancelClick: (row: FieldEventRowModel) => () => void,
  handleDeleteClick: (row: FieldEventRowModel) => () => Promise<boolean>,
  updateNoPracticeObservation: UpdateNoPracticeObservation,
  isInEditMode: (id: Lab.GridRowId) => boolean,
  formatMessage: FormatMessage,
  rowIsLoading: Array<Lab.GridRowId>,
  canBypassProjectFieldEventLock: boolean = false,
  shouldDisableRowSelection: boolean = false,
  selectedFieldEventsOnly: Array<Lab.GridRowId>
) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const columnsToReturn: Array<Lab.GridColDef<FieldEventRowModel<T>>> = [
    {
      field: 'cultivationCycle',
      headerName: formatMessage({
        id: `form.headers.cultivationCycle`,
        defaultMessage: 'Cultivation Cycle',
      }),
      minWidth: 180,
      sortable: false,
      renderHeader: ({field}) => {
        const canPinColumn = !isEmptyObject(apiRef.current.state.pinnedColumns);

        return (
          <Box
            display="flex"
            alignContent="center"
            justifyContent="space-between"
            width={'100%'}
            sx={theme => ({
              '.cc-pinning-button': {
                opacity: 0,
                transition: theme.transitions.easing.easeInOut,
              },
              '&&:hover .cc-pinning-button': {
                opacity: 1,
              },
            })}
          >
            {formatMessage({id: 'Cultivation Cycle', defaultMessage: 'Cultivation Cycle'})}
            <Box display={'flex'} alignItems="center">
              {canPinColumn && <CultivationCyclesPinningButton field={field} />}
              <Tooltip
                title={formatMessage({
                  id: FieldEventTableHeaderTooltips.shared.cultivation_cycle.id,
                  defaultMessage:
                    FieldEventTableHeaderTooltips.shared.cultivation_cycle.defaultMessage,
                })}
                placement="top"
              >
                <Box display="flex" alignItems="center">
                  <SvgIcon type="info-circled" color="secondary" fontSize="h5" />
                </Box>
              </Tooltip>
            </Box>
          </Box>
        );
      },
      renderCell: ({row}) => {
        const showCultivationCycle = row?.meta?.showCultivationCycle;
        if (isDefined(showCultivationCycle) && !isTruthy(showCultivationCycle)) {
          return null;
        }
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        const updatedRow = apiRef.current.getRowWithUpdatedValues(
          row.id,
          ''
        ) as FieldEventRowModel<T>;

        return (
          <CultivationCycleCell cultivationCycle={row?.meta?.cultivationCycle} row={updatedRow} />
        );
      },
    },
    {
      ...Lab.GRID_CHECKBOX_SELECTION_COL_DEF,
      renderHeader: () => {
        const api = apiRef.current;

        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          const allRowIds = Array.from(api.getRowModels().keys());
          api.selectRows(
            allRowIds,
            event.target.checked, // true = select all, false = deselect all
            true
          );
        };

        const allRows = Array.from(api.getRowModels().keys());

        const rowsInViewMode = allRows.filter(rowId => {
          return api.getRowMode(rowId) === Lab.GridRowModes.View;
        });

        const someSelected = rowsInViewMode.some(rowId => {
          return api.isRowSelected(rowId);
        });

        const noSelectableRows = rowsInViewMode.length === 0;

        return (
          <Tooltip
            title={
              shouldDisableRowSelection
                ? formatMessage({
                    id: 'FieldEventsTable.Row.disableRowSelection',
                    defaultMessage: 'You cannot select from more than one field at a time.',
                  })
                : noSelectableRows
                ? formatMessage({
                    id: 'FieldEventsTable.Row.unsavedEvents',
                    defaultMessage: 'You cannot select unsaved events.',
                  })
                : ''
            }
          >
            <Box display="flex" justifyContent="center">
              <Checkbox
                disabled={shouldDisableRowSelection || noSelectableRows}
                onChange={handleChange}
                checked={!shouldDisableRowSelection && someSelected}
                indeterminate={!shouldDisableRowSelection && selectedFieldEventsOnly.length > 0}
              />
            </Box>
          </Tooltip>
        );
      },
      renderCell: params => {
        const rowIsSelectable = apiRef.current.isRowSelectable(params.id);
        const row = params.row;
        const isUnsaved = !!row.meta.isUnsavedEvent;
        const isSelected = selectedFieldEventsOnly.includes(params.id);
        const hasNPO = eventType
          ? isNPOSet(row.meta, NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE[eventType])
          : null;

        const toolTipTitle = shouldDisableRowSelection
          ? formatMessage({
              id: 'FieldEventsTable.Row.disableRowSelection',
              defaultMessage: 'You cannot select from more than one field at a time.',
            })
          : hasNPO
          ? formatMessage({
              id: `FieldEventsTable.Row.hasNPO`,
              defaultMessage: `There are no events to select in this crop year.`,
            })
          : isUnsaved || !rowIsSelectable
          ? formatMessage({
              id: 'FieldEventsTable.Row.unsavedEvent',
              defaultMessage: 'You cannot select an unsaved event.',
            })
          : '';

        const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
          event.stopPropagation();
          apiRef.current.selectRow(params.id, event.target.checked);
        };

        return (
          <Tooltip title={toolTipTitle}>
            <Box>
              <Checkbox
                id="select row"
                onChange={handleChange}
                checked={!shouldDisableRowSelection && isSelected}
                disabled={hasNPO || isUnsaved || !rowIsSelectable}
              />
            </Box>
          </Tooltip>
        );
      },
    },
    ...columns,
    {
      field: 'actions',
      type: 'actions',
      headerName: formatMessage({
        id: `form.headers.Actions`,
        defaultMessage: 'Actions',
      }),
      width: 80,
      headerAlign: 'left',
      cellClassName: 'actions',
      align: 'left',
      getActions: ({id, row}: Lab.GridRowParams<FieldEventRowModel>) => {
        if (rowIsLoading.includes(row.id)) {
          return [
            <Lab.GridActionsCellItem
              key={`loading-row-${row.id}`}
              aria-label={`Row loading ${row.id}`}
              label={formatMessage({id: 'Loading', defaultMessage: 'Loading'})}
              icon={<CircularProgress size={20} />}
              disabled={true}
            />,
          ];
        }

        if (!canBypassProjectFieldEventLock && (row.meta.isLocked || row.meta.isReadOnly)) {
          return [
            <Tooltip
              title={formatMessage({
                id: 'FieldEventsTable.Row.locked',
                defaultMessage: 'This cultivation cycle cannot be edited',
              })}
              key={`lock-row-${row.id}`}
            >
              <span>
                <Lab.GridActionsCellItem
                  aria-label={`Row locked ${row.id}`}
                  label={formatMessage({id: 'Locked', defaultMessage: 'Locked'})}
                  icon={<SvgIcon type="lock" color="secondary" fontSize="h4" />}
                  disabled={true}
                />
              </span>
            </Tooltip>,
          ];
        }

        if (isInEditMode(id)) {
          const rowVals = apiRef.current.getRowWithUpdatedValues(id, '');
          const rowEditModel = apiRef.current.state.editRows[id];
          const rowHasErrors = rowEditModel && rowHasValidationErrors(rowEditModel);
          const isDisabled = rowHasErrors || !canSaveFieldEvent(eventType, rowVals);

          const actions = [
            <Tooltip
              key="save-button"
              title={
                isDisabled
                  ? formatMessage({
                      id: `stage.${stageType}.form.missingRequiredEventData`,
                      defaultMessage: 'Missing required data to save event',
                    })
                  : undefined
              }
            >
              <span>
                <Lab.GridActionsCellItem
                  aria-label={`Save event ${row.id}`}
                  icon={<SvgIcon type="save" color={!isDisabled ? 'primary' : 'secondary'} />}
                  label={formatMessage({id: 'Save', defaultMessage: 'Save'})}
                  onClick={handleToggleRowEditMode(row.id, false)}
                  disabled={isDisabled}
                  sx={{
                    '.MuiSvgIcon-root': {
                      opacity: !isDisabled ? '1' : '0.5',
                    },
                  }}
                />
              </span>
            </Tooltip>,
          ];

          const isNPOValueSet = eventType
            ? isNPOSet(rowVals.meta, NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE[eventType])
            : null;
          const isNPOValueToggledOff = isNPOValueSet !== null && isNPOValueSet;
          const isClearableRow = !!(
            row.meta.eventCount > 0 ||
            row.meta.isUnsavedEvent ||
            isNPOValueToggledOff
          );

          if (isClearableRow) {
            actions.push(
              <Lab.GridActionsCellItem
                key="cancel-button"
                aria-label={`Cancel edit event ${row.id}`}
                icon={<SvgIcon type="cross" color="main" />}
                label={formatMessage({id: 'Cancel', defaultMessage: 'Cancel'})}
                onClick={handleCancelClick(row)}
              />
            );
          }

          return actions;
        }

        if (!row?.meta.eventCount) return [];

        return [
          <Lab.GridActionsCellItem
            key="edit-button"
            aria-label={`Edit event ${row.id}`}
            icon={<SvgIcon type="pencil" color={'main'} />}
            label={formatMessage({id: 'Edit', defaultMessage: 'Edit'})}
            onClick={handleToggleRowEditMode(row.id, true)}
          />,
          <DeleteFieldEventButton
            key="delete-button"
            formatMessage={formatMessage}
            handleClick={handleDeleteClick(row)}
            rowId={row.id}
          />,
        ];
      },
    },
  ];

  const noPracticeType =
    stageType && eventType ? NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE[eventType] : null;

  if (noPracticeType) {
    const npoColumn = {
      ...getColumn({
        attributeType: getNoPracticeObservationHeader(noPracticeType) || '',
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
        stageType: stageType as StageTypes,
        formatMessage,
      }),
      type: 'text',
      editable: false,
      minWidth: 120,
      maxWidth: 120,
      resizable: false,
      renderCell: ({row}: {row: FieldEventRowModel<T>}) => {
        return (
          <NoPracticeObservationCell
            meta={row.meta}
            noPracticeType={noPracticeType}
            updateNoPracticeObservation={updateNoPracticeObservation}
            canBypassProjectFieldEventLock={canBypassProjectFieldEventLock}
          />
        );
      },
    };
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    columnsToReturn.splice(1, 0, npoColumn as Lab.GridColDef<FieldEventRowModel<T>>);
  }

  return columnsToReturn;
};

export const getReferenceDate = (
  row: Pick<
    Partial<FieldEventRowModel>,
    'start_date' | 'harvest_date' | 'end_date' | 'planting_date' | 'meta'
  >
): Date | null => {
  const potentialDates = [
    row?.start_date,
    row?.harvest_date,
    row?.end_date,
    row?.planting_date,
    row?.meta?.cultivationCycle?.start_date,
    row?.meta?.cultivationCycle?.end_date,
  ];
  const rowDate = potentialDates.find(date => typeof date === 'string');

  return rowDate ? convertStringToDate(rowDate) : null;
};

type TooltipColumnHeaderMessage = {
  id: string;
  defaultMessage: string;
  values?: Record<string, string>;
};

export const getColumnHeaderTooltip = <
  T extends keyof typeof FieldEventTableHeaderTooltips,
  F extends keyof (typeof FieldEventTableHeaderTooltips)[T],
>(
  eventType: T,
  field: F | string
): TooltipColumnHeaderMessage | null => {
  const eventMap = FieldEventTableHeaderTooltips[eventType];

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const isField = field in eventMap ? eventMap[field as keyof typeof eventMap] : undefined;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const tooltip = (isString(field) && isField) as TooltipColumnHeaderMessage | undefined;

  if (!tooltip) {
    return null;
  }

  return {
    id: tooltip.id,
    defaultMessage: tooltip.defaultMessage,
    values: tooltip.values,
  };
};
