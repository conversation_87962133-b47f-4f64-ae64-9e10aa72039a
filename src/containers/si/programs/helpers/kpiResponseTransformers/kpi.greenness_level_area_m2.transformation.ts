import sum from 'lodash/sum';

import {type MeasurementEnum} from '_common/utils/measurement-unit-options';
import {getTypedValues} from '_common/utils/object';
import {isDefined, isNil} from '_common/utils/typeGuards';

import type {GreennessLevelMetricData} from 'containers/si/api/apiTypes';
import {convertValue, getUnit} from 'containers/si/utils/convert';
import {formatLargeNumbers} from 'containers/si/utils/formatters/number.format';
import type {Metric, UnitDetail, UnitType} from 'containers/si/utils/value.types';

export type GreennessLevelMetrics = {
  bareArea: Metric;
  lowArea: Metric;
  moderateArea: Metric;
  highArea: Metric;
  unknownArea: Metric;
  notApplicableArea: Metric;
};

export const private__greennessLevelAreaUnit = (unitsSystem: MeasurementEnum) =>
  getUnit(unitsSystem);

export const GREENNESSLEVEL_FORMATTER_MAP: Record<
  keyof GreennessLevelMetrics,
  (value: number) => string
> = {
  bareArea: formatLargeNumbers,
  lowArea: formatLargeNumbers,
  moderateArea: formatLargeNumbers,
  highArea: formatLargeNumbers,
  unknownArea: formatLargeNumbers,
  notApplicableArea: formatLargeNumbers,
};

export const GREENNESSLEVEL_UNIT_MAP: Record<
  keyof GreennessLevelMetrics,
  (unitsSystem: MeasurementEnum) => UnitDetail<UnitType>
> = {
  bareArea: private__greennessLevelAreaUnit,
  lowArea: private__greennessLevelAreaUnit,
  moderateArea: private__greennessLevelAreaUnit,
  highArea: private__greennessLevelAreaUnit,
  unknownArea: private__greennessLevelAreaUnit,
  notApplicableArea: private__greennessLevelAreaUnit,
};

/**
 * This should only be used as a transformer alone with one of the standard kpi.transformation.helpers
 * Example usage:
 * const metricData = makeTopLevelMetric(response, makeCoverCropMetrics(MeasurementEnum.Metric));
 */
export const makeGreennessLevelMetrics =
  (unitsSystem: MeasurementEnum) =>
  (data: GreennessLevelMetricData): GreennessLevelMetrics | null => {
    const {
      greenness_level_bare_area_m2,
      greenness_level_low_area_m2,
      greenness_level_moderate_area_m2,
      greenness_level_high_area_m2,
      greenness_level_unknown_area_m2,
      greenness_level_not_applicable_area_m2,
    } = data;

    // These can be removed once a response deserializer and type validator is built
    // https://regrow.atlassian.net/browse/SI-1379
    // This method should handle
    // expected nulls or undefineds and
    // 0 and Finite checks as those are valid numbers
    if (
      getTypedValues(data).every(isNil) ||
      getTypedValues(data).some(v => isDefined(v) && !Number.isFinite(v)) ||
      sum(getTypedValues(data).map(v => v ?? 0)) === 0
    ) {
      return null;
    }

    // NOTE: This coalesces to 0 ONLY while auto-gen types allow an undefined values for area;
    // we do NOT want the FE to do this anywhere else in the app and this will be removed when the types are updated.
    const bareAreaValue = convertValue({
      value: greenness_level_bare_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });
    const lowAreaValue = convertValue({
      value: greenness_level_low_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });
    const moderateAreaValue = convertValue({
      value: greenness_level_moderate_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });
    const highAreaValue = convertValue({
      value: greenness_level_high_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });
    const unknownAreaValue = convertValue({
      value: greenness_level_unknown_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });
    const notApplicableAreaValue = convertValue({
      value: greenness_level_not_applicable_area_m2 ?? 0,
      from: 'm2',
      to: unitsSystem,
    });

    return {
      bareArea: {
        value: bareAreaValue,
        unit: GREENNESSLEVEL_UNIT_MAP.bareArea(unitsSystem).unit,
        formattedValue: GREENNESSLEVEL_FORMATTER_MAP.bareArea(bareAreaValue),
      },
      lowArea: {
        value: lowAreaValue,
        unit: GREENNESSLEVEL_UNIT_MAP.lowArea(unitsSystem).unit,
        formattedValue: GREENNESSLEVEL_FORMATTER_MAP.lowArea(lowAreaValue),
      },
      moderateArea: {
        value: moderateAreaValue,
        unit: GREENNESSLEVEL_UNIT_MAP.moderateArea(unitsSystem).unit,
        formattedValue: GREENNESSLEVEL_FORMATTER_MAP.moderateArea(moderateAreaValue),
      },
      highArea: {
        value: highAreaValue,
        unit: GREENNESSLEVEL_UNIT_MAP.highArea(unitsSystem).unit,
        formattedValue: GREENNESSLEVEL_FORMATTER_MAP.highArea(highAreaValue),
      },
      unknownArea: {
        value: unknownAreaValue,
        unit: GREENNESSLEVEL_UNIT_MAP.unknownArea(unitsSystem).unit,
        formattedValue: GREENNESSLEVEL_FORMATTER_MAP.unknownArea(unknownAreaValue),
      },
      notApplicableArea: {
        value: notApplicableAreaValue,
        unit: GREENNESSLEVEL_UNIT_MAP.notApplicableArea(unitsSystem).unit,
        formattedValue: GREENNESSLEVEL_FORMATTER_MAP.notApplicableArea(notApplicableAreaValue),
      },
    };
  };
